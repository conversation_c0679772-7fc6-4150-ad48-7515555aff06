import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/data/models/guardian/guardian_ward_model.dart';
import 'package:samitivej_flutter_app/services/guardian_service.dart';

class RxGuardianId extends Rx<int> {
  RxGuardianId(super.initial);

  bool get isParent => value == 0;
  bool get isGuardian => value != 0;
}

class GuardianController extends GetxController {
  GuardianController._privateConstructor();

  static final GuardianController _instance =
      GuardianController._privateConstructor();

  factory GuardianController() {
    return _instance;
  }

  final UserApp _userApp = Get.find<UserApp>();

  /// 0 is the parent account(Well Customer)
  RxGuardianId currentGuardianId = RxGuardianId(0);

  int get currentGuardianIndex => indexFromGuardianId(currentGuardianId.value);

  final Rx<GuardianWard?> _rxCurrentGuardianWard = Rx<GuardianWard?>(null);
  GuardianWard? get currentGuardian => _rxCurrentGuardianWard.value;
  Rx<GuardianWard?> get currentGuardianRx => _rxCurrentGuardianWard;

  /// This will update the current switch account
  set currentGuardian(GuardianWard? value) {
    _rxCurrentGuardianWard.value = value;
    currentGuardianId.value = value?.id ?? 0;
    ssid.value = value?.id.toString() ?? "";
  }

  RxString ssid = RxString("");

  final List<Color> colorList = [
    AppColor.primaryGreen4,
    AppColor.homeVaccineButton,
    AppColor.green400,
  ];

  List<GuardianWard> _guardians = List.from(<GuardianWard>[]);

  void toggleMock() {
    useMock = !useMock;
    if (useMock) {
      Get.snackbar(
        "Toggle result",
        "You become guardian by Mocking Data. Refresh to see result.",
        duration: const Duration(milliseconds: 1000),
      );
    } else {
      Get.snackbar(
        "Toggle result",
        "You are using data from API. Refresh to see result.",
        duration: const Duration(milliseconds: 1000),
      );
    }
  }

  final String runMode = dotenv.env['RUN_MODE']!;

  bool get canMock => runMode == "dev";

  final RxBool _useMock = RxBool(false);

  bool get useMock => _useMock.value;

  set useMock(bool value) {
    if (canMock) {
      _useMock.value = value;
    }
  }

  RxBool hasGuardian = RxBool(false);

  RxBool loadingGuardianList = RxBool(false);
  RxString fetchGuardianError = RxString("");

  RxBool loadingSwitchGuardian = RxBool(false);
  RxString switchGuardianError = RxString("");

  int indexFromGuardianId(int userId) {
    return guardianAndMe.indexWhere((element) => element.id == userId);
  }

  Future<void> fetchGuardianList() async {
    try {
      loadingGuardianList.value = true;
      fetchGuardianError.value = "";
      if (useMock) {
        await Future.delayed(const Duration(seconds: 1));
        final pool = <String>[
          "Yunus Pham",
          "Clementine Mcknight",
          "Connor Cervantes",
          "Monty Conley",
          "Cory Andrade",
          "Asia Hernandez",
          "Norma Lam",
          "Bernard Cantu",
          "Donald Reid",
          "Chiara Fitzgerald"
        ];
        _guardians = pool.where((_) {
          final rng = Random();
          return rng.nextInt(100) > 80;
        }).map((e) {
          final name = e.split(" ");
          return GuardianWard(
            id: pool.indexOf(e) + 1,
            firstName: name[0],
            lastName: name[1],
            isCurrentAccount: true,
            privilegesGroupId: 1,
            privilegesList: "",
          );
        }).toList();
        if (_guardians.isEmpty) {
          _guardians.add(GuardianWard(
              id: 1,
              firstName: "Zorro",
              lastName: "Gertruida",
              isCurrentAccount: true,
              privilegesGroupId: 1,
              privilegesList: ""));
        }
        _guardians.shuffle();
        currentGuardianId.value = 0;
      } else {
        _guardians = await GuardianService.getGuardians();
        if (_guardians.currentWard != null) {
          currentGuardian = _guardians.currentWard;
        } else {
          await switchGuardian(0);
        }
      }
      // If it has only one guardian, that it is the parent account
      hasGuardian.value = _guardians.length > 1;
    } on DioException {
      fetchGuardianError.value = "An error occurs";
      _guardians.clear();
      hasGuardian.value = false;
      currentGuardianId.value = 0;
    } catch (e) {
      fetchGuardianError.value = "An error occurs";
      _guardians.clear();
      hasGuardian.value = false;
      currentGuardianId.value = 0;
    } finally {
      loadingGuardianList.value = false;
    }
  }

  void clear() {
    _guardians.clear();
    hasGuardian.value = false;
    currentGuardianId.value = 0;
  }

  Future<void> switchGuardian(int userId) async {
    try {
      loadingSwitchGuardian.value = true;
      final isSuccess = await GuardianService.swithchAccount(userId);
      if (isSuccess) {
        await fetchGuardianList();
      }
    } on DioException catch (e) {
      switchGuardianError.value = e.message ?? "An error with API";
    } catch (e) {
      switchGuardianError.value = "An error occurs";
    } finally {
      loadingSwitchGuardian.value = false;
    }
  }

  String createPlaceHolder(String firstName, String lastName) {
    final thaiAlphabet =
        RegExp(r'[กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮ]');

    final fullName = "$firstName $lastName";

    String firstLetter = "";
    String lastLetter = "";

    if (thaiAlphabet.hasMatch(fullName)) {
      final firstMatch = thaiAlphabet.firstMatch(firstName);
      firstLetter = (firstMatch != null)
          ? firstName.substring(firstMatch.start, firstMatch.start + 1)
          : firstName[0];
      final lastMatch = thaiAlphabet.firstMatch(lastName);
      lastLetter = (lastMatch != null)
          ? lastName.substring(lastMatch.start, lastMatch.start + 1)
          : lastName[0];
    } else {
      firstLetter = firstName[0];
      lastLetter = lastName[0];
    }
    return (firstLetter + lastLetter).toUpperCase();
  }

  GuardianWardList get guardianAndMe {
    return _guardians;
  }

  Color getColor(int index) {
    final isParent =
        (index >= 0) ? guardianAndMe.elementAt(index).id == 0 : false;
    return isParent ? AppColor.primaryGreen4 : AppColor.homeVaccineButton;
  }

  Color get selectedColor {
    return getColor(indexFromGuardianId(currentGuardianId.value));
  }

  Color getPastelColor(int index) {
    final isParent =
        (index >= 0) ? guardianAndMe.elementAt(index).id == 0 : false;
    return isParent ? AppColor.green550 : AppColor.blue400;
  }

  Color get selectedPastelColor {
    return getPastelColor(indexFromGuardianId(currentGuardianId.value));
  }

  String getName(int index) {
    return (index >= 0) ? guardianAndMe.elementAt(index).fullName : "Well User";
  }

  String get selectedName {
    return getName(indexFromGuardianId(currentGuardianId.value));
  }

  String getFirstName(int index) {
    return (index >= 0) ? guardianAndMe.elementAt(index).firstName : "Well";
  }

  String get selectedFirstName {
    return getFirstName(indexFromGuardianId(currentGuardianId.value));
  }

  String getLastName(int index) {
    return (index >= 0) ? guardianAndMe.elementAt(index).lastName : "User";
  }

  String get selectedLastName {
    return getLastName(indexFromGuardianId(currentGuardianId.value));
  }

  String getImageUrl(int index) {
    if (index == 0) {
      return _userApp.userProfileImageUrl.value.url ?? "";
    }
    return "";
  }

  String get selectedImageUrl {
    return getImageUrl(indexFromGuardianId(currentGuardianId.value));
  }

  String getCustomerGuardianHash() {
    String stringToHash = "";
    String customerSsid = _userApp.customerData.value.customer?.ssid ?? "";
    if (currentGuardianId.isGuardian) {
      stringToHash = customerSsid + ssid.value;
    } else {
      stringToHash = customerSsid + customerSsid;
    }
    return sha256.convert(utf8.encode(stringToHash)).toString();
  }
}
