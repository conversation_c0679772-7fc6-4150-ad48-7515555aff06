import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiConstant {
  ApiConstant._();

  static String cognitoPlusUrl = dotenv.env['COGNITO_PLUS_URL']!;

  static String baseCognitoPlusUrl = dotenv.env['BASE_COGNITO_PLUS_URL']!;

  static String homeAppDomain = dotenv.env['HOME_APP_DOMAIN']!;
  static String totalHealthDomain = dotenv.env['TOTAL_HEALTH_DOMAIN']!;
  static String fitDomain = dotenv.env['FIT_DOMAIN']!;
  static String foodDomain = dotenv.env['FOOD_DOMAIN']!;
  static String foodFitDomain = dotenv.env['FOODFIT_DOMAIN']!;

  static String totalHealthWebDomain = dotenv.env['TOTAL_HEALTH_WEB_DOMAIN']!;
  static String fitWebDomain = dotenv.env['FIT_WEB_DOMAIN']!;
  static String foodWebDomain = dotenv.env['FOOD_WEB_DOMAIN']!;

  static String appointmentDomain = dotenv.env['APPOINTMENT_DOMAIN']!;

  static String virtualDomain = dotenv.env['VIRTUAL_DOMAIN']!;
  static String paymentDomain = dotenv.env['PAYMENT_DOMAIN']!;

  static String paymentWebDomain = dotenv.env['PAYMENT_WEB_DOMAIN']!;

  static String chatDomain = dotenv.env['CHAT_DOMAIN']!;

  static String mitrmomWebDomain = dotenv.env['MITRMOM_WEB_DOMAIN']!;
  static String geneTestWebDomain = dotenv.env['GENETEST_WEB_DOMAIN']!;

  static String geneTestWebTHDomain =
      '${geneTestWebDomain}th/?utm_source=well&utm_medium=service_th&utm_campaign=genetest';
  static String geneTestWebENDomain =
      '${geneTestWebDomain}en/?utm_source=well&utm_medium=service_en&utm_campaign=genetest';

  static String medRefillWebDomain = dotenv.env['MEDREFILL_WEB_DOMAIN']!;

  static String vaccineJourneyWebDomain =
      dotenv.env['VACCINE_JOURNEY_WEB_DOMAIN']!;

  static String queueDomain = dotenv.env['QUEUE_DOMAIN']!;

  static String aidoctorDomain = dotenv.env['AIDOCTOR_DOMAIN']!;

  static String corpDomain = dotenv.env['CORP_DOMAIN']!;

  static String fitStravaDomain = dotenv.env['FITSTRAVA_DOMAIN']!;

  static String unleashDomain = dotenv.env['UNLEASH_DOMAIN']!;

  static String wearableDomain = dotenv.env['WEARABLE_DOMAIN']!;
  static String connectDeviceDomain = dotenv.env['CONNECT_DEVICE_DOMAIN']!;
  static String guardianModeDomain =
      '${dotenv.env['GUARDIAN_MODE_DOMAIN']!}/${dotenv.env['RUN_MODE']!}/';
  static String guardianModeWebDomain = dotenv.env['GUARDIAN_MODE_WEB_DOMAIN']!;
  static String guardianModeGetConsent =
      '${guardianModeDomain}mobile/terms-and-conditions';
  static String guardianModeSetConsent =
      '${guardianModeDomain}mobile/accept-terms';
  static String senseDomain = dotenv.env['SENSE_DOMAIN']!;

  static String apiVersion = 'api/v4/';

  static String appflyerKey = dotenv.env['AF_DEV_KEY']!;
  static String appflyerId = dotenv.env['APP_ID']!;

  static String sendOTP = '${cognitoPlusUrl}api/v5/user/otp';
  static String confirmOTP = '${cognitoPlusUrl}api/v5/user/check-otp';
  static String checkMigrateUserPlus =
      '${cognitoPlusUrl}api/v5/user/check-migrate';
  static String requestMigrateUserPlus =
      '${cognitoPlusUrl}api/v5/user/request-migrate';
  static String confirmMigrateUserPlus =
      '${cognitoPlusUrl}api/v5/user/confirm-migrate';
  static String checkCustomerHn = 'api/v5/user/check-hn';
  static String listGuardians = '${guardianModeDomain}mobile/ward/privilege';
  static String switchGuardian =
      '${guardianModeDomain}mobile/ward/switch-account';
  static String totalHealthAuthActiveMfa =
      '${totalHealthDomain}auth/setmfa/token';
  static String totalHealthPasswordSalt =
      '${totalHealthDomain}total-health-solution/customer/getSalt';

  static String getBanner = '${homeAppDomain}home/banner';
  static String getCouponUrl = '${homeAppDomain}home/coupon/get';
  static String getDiscover = '${homeAppDomain}home/discover/get';
  static String getDiscoverDetail = '${homeAppDomain}home/discover/detail/get';
  static String getSessionActivity =
      '${homeAppDomain}home/session-activity/get';
  static String insertSessionActivity =
      '${homeAppDomain}home/session-activity/insert/recent';
  static String getMood = '${homeAppDomain}home/mood/get';
  static String getMembershipHome = '${homeAppDomain}home/membership/home';
  static String getMoodsByDateRange = '${homeAppDomain}home/mood/dates/get';
  static String insertMood = '${homeAppDomain}home/mood/insert';
  static String productFeed = '${homeAppDomain}home/product-feed';
  static String getHealthInfo2 = '${homeAppDomain}home/healthInfo/get';
  static String getNotificationToken =
      '${homeAppDomain}home/notification/get-customer-notification-token';
  static String insertNotificationToken =
      '${homeAppDomain}home/notification/insert-notification-token';
  static String updateNotificationToken =
      '${homeAppDomain}home/notification/update-notification-language';
  static String deleteNotificationToken =
      '${homeAppDomain}home/notification/delete-expired-notification-token';
  static String getNotificationList =
      '${homeAppDomain}home/notification/get-notification-histories';
  static String updateBadgeNotification =
      '${homeAppDomain}home/notification/update-badge-status';
  static String updateBadgeNotificationById =
      '${homeAppDomain}home/notification/update-badge-status-by-id';
  static String updateNotificationStatus =
      '${homeAppDomain}home/notification/status';
  static String updateChatNotification =
      '${homeAppDomain}home/notification/update-chat-badge-status';
  static String getPopup = '${homeAppDomain}home/popup';

  static String totalHealthInsertCustomer =
      '${totalHealthDomain}total-health-solution/customer/insert';
  static String totalHealthGetCustomer =
      '${totalHealthDomain}total-health-solution/customer/min/get';
  static String totalHealthUpdateCustomer =
      '${totalHealthDomain}total-health-solution/customer/update';
  static String totalHealthDeleteCustomer =
      '${totalHealthDomain}total-health-solution/customer/delete';
  static String totalHealthGetCustomerHn =
      '${totalHealthDomain}total-health-solution/customer/hn/get';

  static String totalHealthCheckMail =
      '${totalHealthDomain}total-health-solution/customer/email/check';

  static String totalHealthCreatePin =
      '${totalHealthDomain}total-health-solution/customer/pin/insert';
  static String totalHealthCheckPin =
      '${totalHealthDomain}total-health-solution/customer/pin/check';
  static String totalHealthUpdatePin =
      '${totalHealthDomain}total-health-solution/customer/pin/update';
  static String totalHealthResetPin =
      '${totalHealthDomain}total-health-solution/customer/pin/forget';
  static String totalHealthRevokePinToken =
      '${totalHealthDomain}total-health-solution/customer/pin/token/revoke';
  static String totalHealthRegisterDeviceBio =
      '${totalHealthDomain}total-health-solution/customer/pin/bio/device/register';

  static String totalHealthCreateZendesk =
      '${totalHealthDomain}total-health-solution/zendesk/ticket';
  static String totalHealthCreateSignupZendesk =
      '${totalHealthDomain}total-health-solution/zendesk/ticket/signup';

  static String getDailyCoinStatus = '${homeAppDomain}home/coin/diary/get';
  static String getTotalCoins = '${homeAppDomain}home/coin/get';
  static String getCoinHistory = '${homeAppDomain}home/coin/history/get';
  static String getUsedCoinHistory =
      '${homeAppDomain}home/coin/history/used/get';
  static String getEarnedCoinHistory =
      '${homeAppDomain}home/coin/history/received/get';
  static String claimDailyCoins = '${homeAppDomain}home/coin/diary/update';

  static String plusUpdateUser = '${cognitoPlusUrl}api/v5/user/update';
  static String getUpcomingAppointments =
      '${cognitoPlusUrl}api/v5/appointment/schedule';
  static String getPastAppointments = '${cognitoPlusUrl}api/v5/history/list';

  static String getBodyPart = '${appointmentDomain}prescreening/body-part';
  static String getOrgan = '${appointmentDomain}prescreening/organ';
  static String getSymptoms = '${appointmentDomain}prescreening/symptom';

  static String getAllAppointments =
      '${appointmentDomain}appointment/upcoming?scope=all';
  static String getGuardianUpcomingAppointments =
      '${appointmentDomain}appointment/upcoming?scope=guardian';
  static String getAppointment = '${appointmentDomain}appointment/get';
  static String createAppointment = '${appointmentDomain}appointment/create';
  static String doctorBookableInfo =
      '${appointmentDomain}appointment/doctor/bookable';
  static String bookingAppointment = '${appointmentDomain}appointment/book';
  static String updateAppointment = '${appointmentDomain}appointment/update';
  static String deleteAppointment = '${appointmentDomain}appointment/deleted';
  static String getHospitals = '${appointmentDomain}appointment/hospital/get';
  static String getInterpreters = '${appointmentDomain}appointment/interpreter';
  static String getPatient = '${appointmentDomain}appointment/patient/get';
  static String saveSearch = '${appointmentDomain}appointment/search/insert';
  static String getHospitalContact =
      '${appointmentDomain}appointment/specialty/tel';
  static String getRecentSearch =
      '${appointmentDomain}appointment/search/history';
  static String getNationalities =
      '${appointmentDomain}appointment/nationality';
  static String getSpecialties =
      '${appointmentDomain}appointment/specialty/get/all';

  static String checkVirtual =
      '${appointmentDomain}appointment/doctor/virtual/check';
  static String getFavoriteDoctors =
      '${appointmentDomain}appointment/doctor/favorite/get-detail';
  static String getFavoriteDoctor =
      '${appointmentDomain}appointment/doctor/favorite/get';
  static String favoriteDoctor =
      '${appointmentDomain}appointment/doctor/favorite/insert';
  static String unFavoriteDoctor =
      '${appointmentDomain}appointment/doctor/favorite/delete';
  static String doctorLanguages =
      '${appointmentDomain}appointment/doctor/language';
  static String getDoctorTimeAvalible =
      '${appointmentDomain}datacenter/doctor/slot';
  static String suggestDoctors =
      '${appointmentDomain}datacenter/doctor/group/search';
  static String recommendedDoctor =
      '${appointmentDomain}datacenter/doctor/group/search/recommendation';

  static String getPaceUrl = '${appointmentDomain}pace/url';
  static String getPaceShareUrl = '${appointmentDomain}pace/share/url';
  static String getPaceShareUrlV2 = '${appointmentDomain}pace/v2/share/url';
  static String getPromptUrl = '${appointmentDomain}prompt/url';
  static String getPromptShareUrl = '${appointmentDomain}prompt/share/url';

  static String getDoctorProfile =
      '${appointmentDomain}datacenter/doctor/profile';
  static String searchDoctors = '${cognitoPlusUrl}api/v5/doctor/search';

  static String getDailyFoodTaskPoints =
      '${foodDomain}food-api/dailytask/calendar/get';
  static String getRegisteredFoodPlan =
      '${foodDomain}food-api/plan/get-current-registered-plan';
  static String getDailyFoodTasks = '${foodDomain}food-api/dailytask/get';
  static String updateDailyFoodTask = '${foodDomain}food-api/dailytask/update';
  static String upsertFitUser = '${fitDomain}fit-api/user/upsert';
  static String updateFitDevice = '${fitDomain}fit-api/user/update-device';
  static String connectStrava = '${fitDomain}fit-api/user/strava';
  static String deconnectStrava = '${fitDomain}fit-api/user/strava/deauthorize';
  static String getFitDailyTask = '${fitDomain}fit-api/user/get-task-by-date';
  static String getDailyChallenges =
      '${fitDomain}fit-api/challenge/get-by-date';
  static String getChallengesByDates =
      '${fitDomain}fit-api/challenge/get-by-dates';
  static String getChallenges = '${fitDomain}fit-api/challenge/get';
  static String getDeviceInfo = '${fitDomain}fit-api/user/get';
  static String getGarminRequestToken =
      '${fitDomain}garmin/oauth/request_token';
  static String getGarminAccessToken = '${fitDomain}garmin/oauth/access_token';
  static String getBMI = '${fitDomain}food-api/plan/get-bmi';
  static String updateQuestionnaireData =
      '${totalHealthDomain}total-health-solution/health-assessment/insert';
  static String getGarminPulseInfo = '${fitDomain}garmin/health/epoch/pulse';
  static String getGarminStepInfo = '${fitDomain}garmin/health/epoch/step';
  static String getGarminBurnInfo = '${fitDomain}garmin/health/epoch/burn';
  static String getGarminSleepInfo = '${fitDomain}garmin/health/daily/sleep';
  static String getGarminUserId = '${fitDomain}garmin/user';
  static String upsertHealthData = '${fitDomain}user/health/upsert';
  static String getGarminDailyInfo = '${fitDomain}garmin/health/daily';

  static String getBannerHomeFit = '${homeAppDomain}home/banner?location=Fit';
  static String getCurrentPlanHomeFit =
      '${fitDomain}fit-api/plan/get-current-plans-detail';
  static String getHistoryPlanHomeFit =
      '${fitDomain}fit-api/plan/get-registered-plan-histories';
  static String getStreakHomeFit = '${fitDomain}fit-api/workout/get-steak';
  static String getExceriseHomeFit =
      '${fitDomain}fit-api/activity/get-activity-by-date';
  static String getStepReportHomeFit = '${fitDomain}fit-api/user/health/step';
  static String getCalReportHomeFit =
      '${fitDomain}fit-api/user/health/burned_calories';
  static String getPreferredHomeFit =
      '${fitDomain}fit-api/workout/get-preferred';
  static String insertPreferHomeFit =
      '${fitDomain}fit-api/workout/insert-preferred';
  static String removePreferHomeFit =
      '${fitDomain}fit-api/workout/remove-preferred';
  static String getHealthRiskTestResultHomeFit =
      '${foodFitDomain}foodfit/fit/healthrisk/get';
  static String getPlanHomeFit = '${foodFitDomain}foodfit/fit/plan/get';
  static String changePlanHomeFit = '${fitDomain}fit-api/plan/change-plan';
  static String reportTestHomeFit = '${fitDomain}questionaire?redo=true';
  static String getPendingPaymentInvoices =
      '${paymentDomain}well-payment-api/v2/payment-list';
  static String getPendingPaymentPurchases =
      '${totalHealthDomain}total-health-solution/purchase/get/pending-payment';
  static String getPendingPaymentFoodPurchases =
      '${foodDomain}food-api/purchase/get-purchase-cart';
  static String getPaidPurchases =
      '${totalHealthDomain}total-health-solution/purchase/get/paid';
  static String getPaidFoodPurchases =
      '${foodDomain}food-api/purchase/get-success-purchase-cart';
  static String deleteOrder =
      '${totalHealthDomain}total-health-solution/purchase/order/delete';
  static String deleteFoodOrder =
      '${foodDomain}food-api/purchase/delete-purchase-cart';
  static String calculateDeliveryFee =
      '${foodDomain}food-api/purchase/calculate-delivery-fee';

  static String getFoodPlan =
      '${foodDomain}food-api/plan/get-current-registered-plan';
  static String getFitPlan =
      '${fitDomain}fit-api/plan/get-current-registered-plan';

  static String checkCouponCode =
      '${paymentDomain}well-payment-api/promotion/main/promotion/redeem';
  static String getCouponList =
      '${paymentDomain}well-payment-api/promotion/main/promotion/user/fastpay';
  static String getMerchantIds =
      '${paymentDomain}well-payment-api/master-data/get-merchant-id';
  static String requestCreditCardPayment =
      '${paymentDomain}well-payment-api/gateway/setup-payment';
  static String getCreditCardPaymentStatus =
      '${paymentDomain}well-payment-api/gateway/request-payment-notify';
  static String requestQrCodePayment =
      '${paymentDomain}well-payment-api/gateway/setup-payment';
  static String getQrCodePaymentStatus =
      '${paymentDomain}well-payment-api/gateway/request-payment-notify';
  static String inquirePaymentCustomer =
      '${paymentDomain}well-payment-api/customer/inquiry-customer';
  static String checkQRScanSuccess =
      '${paymentDomain}well-payment-api/gateway/payment-callback';

  static String getProvinces =
      '${totalHealthDomain}total-health-solution/master-data/province/get';
  static String getDistricts =
      '${totalHealthDomain}total-health-solution/master-data/district/get';
  static String getSubDistricts =
      '${totalHealthDomain}total-health-solution/master-data/subdistrict/get';
  static String getPostalCodes =
      '${totalHealthDomain}total-health-solution/master-data/postal/get';
  static String getCustomer =
      '${totalHealthDomain}total-health-solution/customer/get';
  static String getAddresses =
      '${totalHealthDomain}total-health-solution/customer/get-customer-address';
  static String createAddress =
      '${totalHealthDomain}total-health-solution/customer/insert-customer-address';
  static String updateAddress =
      '${totalHealthDomain}total-health-solution/customer/update-customer-address';
  static String deleteAddress =
      '${totalHealthDomain}total-health-solution/customer/delete-customer-address';

  static List<String> get ignoreAuthPath => [
        totalHealthCheckMail,
        totalHealthCreateSignupZendesk,
        totalHealthPasswordSalt,
      ];

  static String getS3Image =
      '${totalHealthDomain}total-health-solution/s3/getImage';

  /* fit API*/
  static final String fitAPI = '${fitDomain}fit-api';

  /* fit user health*/
  static final String userHealth = '$fitAPI/user/health';
  static final String burnedCaloriesUserHealth = '$userHealth/burned_calories';
  static final String stepUserHealth = '$userHealth/step';

  /* fit challenge */
  static final String challenge = '$fitAPI/challenge';
  static final String getChallenge = '$challenge/get';
  static final String registerChallenge = '$challenge/register';
  static final String getDetailChallenge = '$challenge/get-detail';
  static final String getByDatesChallenge = '$challenge/get-by-dates';
  static final String getByDateChallenge = '$challenge/get-by-date';
  static final String updateChallenge = '$challenge/update';

  /*  fit workout */
  static final String workout = '$fitAPI/workout';
  static final String getWorkoutRoutine = '$workout/get-routine';
  static final String startWorkoutRoutine = '$workout/start-routine';
  static final String endWorkoutRoutine = '$workout/end-routine';
  static final String updateWorkoutActivity = '$workout/update-activity';
  static final String updateWorkoutPreferredTure = '$workout/insert-preferred';
  static final String updateWorkoutPreferredFalse = '$workout/remove-preferred';

  /* fit activity */
  static final String activity = '$fitAPI/activity';
  static final String insertActivity = '$activity/insert-activity';
  static final String getActivityByDate = '$activity/get-activity-by-date';

  /* Food Plan */
  static final String foodPlan = '${foodDomain}food-api/plan';
  static final String getPlanFitnessTracker =
      '$foodPlan/get-fitness-tracker-histories';

  /* Fit Plan */
  static final String fitPlan = '${fitDomain}food-api/plan';
  static final String getCurrentPlanFitnessTracker =
      '$fitPlan/get-current-plans-detail';
  static final String updatePlanFitnessTracker =
      '$fitPlan/insert-fitness-tracker-history';
  static final String getStravaToken = '$fitStravaDomain/getStravaToken';

  /* Queue */
  static final String getCurrentQueueStatus = '${queueDomain}status/current';
  static final String getAllQueueStatusList = '${queueDomain}status/all';
  static final String insertQueueRating = '${queueDomain}rating';

  /* Feature Flags */
  static final String unleashFrontendEndpoint = '$unleashDomain/api/frontend';

  /* connect device*/
  static String sleep = "${connectDeviceDomain}api/health/sleep_analysis/new";
  static String nutrition = "${connectDeviceDomain}api/health/nutrition/new";
  static String hearth = "${connectDeviceDomain}api/health/ecg_heart/new";
  static String sport = "${connectDeviceDomain}api/health/sport_cardio/new";
  static String emotion = "${connectDeviceDomain}api/health/emotion/new";
  static String overview = "${connectDeviceDomain}api/health/overview/token/";
  static String healthTracker =
      "${connectDeviceDomain}api/health/overview/health_tracker/";
  static String isNewCustomer =
      "${connectDeviceDomain}api/health/customer/check/";
  static String lastSync = "${connectDeviceDomain}api/health/customer/token/";
  static String checkCustomer =
      "${connectDeviceDomain}api/health/customer/check_customer/";
  static String acceptTerm =
      "${connectDeviceDomain}api/health/customer/accept_term/";
  static String accessWearable =
      "${connectDeviceDomain}api/health/customer/access_wearable/";

  static String senseScore = "${senseDomain}sense-score";
}
