import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' hide SearchBar;
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/apps/appointment/appointment_app.dart';
import 'package:samitivej_flutter_app/apps/appointment/my_appointment_app.dart';
import 'package:samitivej_flutter_app/apps/appointment/plus_appointment_app.dart';
import 'package:samitivej_flutter_app/apps/appointment/suggest_app.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/apps/feature_flags/feature_flags_app.dart';
import 'package:samitivej_flutter_app/commons/services/analytics_service.dart';
import 'package:samitivej_flutter_app/commons/utils/internet_connection_helper.dart';
import 'package:samitivej_flutter_app/components/custom_button.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_layout.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/presentation/pages/samitivej_plus/components/guardian_switcher.dart';
import 'package:samitivej_flutter_app/screen/appointment/components/appointment/appointment_app_bar.dart';
import 'package:samitivej_flutter_app/screen/appointment/components/appointment/appointment_date.dart';
import 'package:samitivej_flutter_app/screen/appointment/components/appointment/appointment_hospital_selection.dart';
import 'package:samitivej_flutter_app/screen/appointment/components/appointment/appointment_require_select_doctor.dart';
import 'package:samitivej_flutter_app/screen/appointment/components/help_finding/help_finding_card.dart';
import 'package:samitivej_flutter_app/screen/appointment/components/search/search_bar.dart'
    show SearchBar;
import 'package:samitivej_flutter_app/screen/appointment/components/appointment/appointment_specialties.dart';
import 'package:samitivej_flutter_app/screen/appointment/doctor_screen.dart';
import 'package:samitivej_flutter_app/screen/appointment/suggest_screen.dart';
import 'package:samitivej_flutter_app/utils/exception_handle.dart';
import 'package:samitivej_flutter_app/utils/utils.dart';

class AppointmentScreen extends StatefulWidget {
  const AppointmentScreen({super.key});

  @override
  State<AppointmentScreen> createState() => _AppointmentScreenState();
}

class _AppointmentScreenState extends State<AppointmentScreen> {
  final scrollController = ScrollController();
  final formWidgetKey = GlobalKey();
  final appointmentApp = Get.find<AppointmentApp>();
  final myAppointmentApp = Get.put(MyAppointmentApp());
  final plusAppointmentApp = Get.find<PlusAppointmentApp>();
  final featureFlagsApp = Get.find<FeatureFlagsApp>();

  Future<void> initialize() async {
    await Future.delayed(Duration.zero);
    await InternetConnectionHelper.executeWithInternetCheck(() async {
      try {
        Get.find<CommonApp>().appLoading.value = true;
        await Future.wait([
          appointmentApp.getSpecialtyList(),
          appointmentApp.getHospitalList(),
          appointmentApp.getinterpretationLanguage(),
          appointmentApp.getNationalities(),
          appointmentApp.getAppointmentPatient(),
          myAppointmentApp.getMyAppointments(),
        ]);
      } on Exception catch (e) {
        ExceptionHandle.exceptionAction(e);
      } finally {
        Get.find<CommonApp>().appLoading.value = false;
      }
    });
  }

  Future<void> onSuggestDoctor(context) async {
    appointmentApp.isSuggestValidated.value = appointmentApp.isSuggestValid;
    if (appointmentApp.isSuggestValid) {
      AnalyticsService.logEventInteraction(
        name: 'Appointment/P.Home/B.FindDoctor',
        customParameters: {
          'branch': appointmentApp.selectedHospital.value?.hospitalCode,
          'specialty': appointmentApp.selectedSpecialties.value,
          'requestSuggest': appointmentApp.selectedRecommendDoctor.value,
        },
      );
      try {
        Get.find<CommonApp>().appLoading.value = true;
        if (appointmentApp.selectedRecommendDoctor.value == true) {
          final doctor = await appointmentApp.getRecommendedDoctor(
              specialityCode: appointmentApp.selectedSpecialties.value,
              date: appointmentApp.selectedAppontmentDate.value,
              site: appointmentApp.selectedHospital.value?.hospitalCode,
              isVirtual: appointmentApp.isAppointmentTypeHospital != null
                  ? !appointmentApp.isAppointmentTypeHospital!
                  : null,
              context: context);
          if (doctor != null) {
            Get.to<void>(() => DoctorScreen(doctor: doctor));
          } else {
            Get.to<void>(
              () => const SuggestScreen(),
              binding: BindingsBuilder.put(
                () => SuggestApp(
                  specialityCode: appointmentApp.selectedSpecialties.value,
                  appointmentDate: appointmentApp.selectedAppontmentDate.value,
                  site: appointmentApp.selectedHospital.value?.hospitalCode,
                  isVirtual: appointmentApp.isAppointmentTypeHospital != null
                      ? !appointmentApp.isAppointmentTypeHospital!
                      : null,
                ),
              ),
            );
          }
        } else {
          Get.to<void>(
            () => const SuggestScreen(),
            binding: BindingsBuilder.put(
              () => SuggestApp(
                specialityCode: appointmentApp.selectedSpecialties.value,
                appointmentDate: appointmentApp.selectedAppontmentDate.value,
                site: appointmentApp.selectedHospital.value?.hospitalCode,
                isVirtual: appointmentApp.isAppointmentTypeHospital != null
                    ? !appointmentApp.isAppointmentTypeHospital!
                    : null,
              ),
            ),
          );
        }
      } on Exception catch (e) {
        if (kDebugMode) {
          print(e);
        }
      } finally {
        Get.find<CommonApp>().appLoading.value = false;
      }
    } else {
      final box = formWidgetKey.currentContext?.findRenderObject() as RenderBox;
      final position = box.localToGlobal(Offset.zero);
      final y = position.dy;
      scrollController.animateTo(
        y,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    initialize();
  }

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
      child: Scaffold(
        backgroundColor: AppColor.white,
        body: GestureDetector(
          onTap: () => AppUtils.unfocus(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppointmentAppBar(
                title: AppLocalizations.of(context)!.findYourDoctor,
              ),
              if (featureFlagsApp.samitivejGuardianSwitcher.value)
                GuardianSwitcher(
                  padding: EdgeInsets.only(
                    top: 10,
                    left: AppLayout.defaultPageHorizontialPadding(context),
                    right: AppLayout.defaultPageHorizontialPadding(context),
                  ),
                  onSwitch: () async {
                    await initialize();
                  },
                ),
              AppLayout.mediumH,
              const SearchBar(),
              AppLayout.smallH,
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async => await initialize(),
                  child: ListView(
                    controller: scrollController,
                    padding: EdgeInsets.zero,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const HelpFindingCard(),
                          AppLayout.mediumH,
                          AppointmentHospitalSelection(key: formWidgetKey),
                          AppLayout.mediumH,
                          const AppointmentSpecialties(),
                          AppLayout.mediumH,
                          const AppointmentDate(),
                          AppLayout.mediumH,
                          // Section prefer doctor or need select doctor
                          const AppointmentRequireSelectDoctor(),
                          AppLayout.mediumH,
                          Center(
                            child: CustomButton(
                              height: AppLayout.defaultTextFieldHeight(context),
                              width: 25.h,
                              padding: EdgeInsets.zero,
                              label: AppTranslateKey.next,
                              labelStyle: AppText.theme(context).titleSmall,
                              onPressed: () async =>
                                  await onSuggestDoctor(context),
                              borderRadiusSize: 50.sp,
                            ),
                          ),
                          AppLayout.largeH,
                        ],
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
