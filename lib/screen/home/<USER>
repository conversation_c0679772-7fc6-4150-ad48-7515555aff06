import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/apps/guardian/guardian_controller.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/components/custom_alert_dialog.dart';
import 'package:samitivej_flutter_app/components/custom_circle_button.dart';
import 'package:samitivej_flutter_app/components/custom_webview.dart';
import 'package:samitivej_flutter_app/components/filled_button_widget.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/customer_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/models/virtual_model.dart';
import 'package:samitivej_flutter_app/presentation/pages/samitivej_plus/components/guardian_switcher.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';

class ConsultNowWebViewScreen extends StatefulWidget {
  const ConsultNowWebViewScreen({super.key, this.partner});

  final String? partner;

  @override
  State<ConsultNowWebViewScreen> createState() =>
      _ConsultNowWebViewScreenState();
}

class _ConsultNowWebViewScreenState extends State<ConsultNowWebViewScreen> {
  String? virtualLink;
  bool virtualLoading = true;
  bool isSelectorHidden = false;

  @override
  Widget build(BuildContext context) {
    String? link = virtualLink;
    return Scaffold(
      backgroundColor: AppColor.white,
      body: SafeArea(
        child: Stack(
          children: [
            if (link != null)
              CustomWebView(
                url: WebUri(link),
                onLoadStop: (controller, url) {
                  final CommonApp commonApp = Get.find<CommonApp>();
                  Future.delayed(const Duration(milliseconds: 1000)).then((_) {
                    controller.evaluateJavascript(source: """
                      changeSelectedLanguage("${commonApp.selectedLanguage.value}");
                    """);
                    setState(() {
                      virtualLoading = false;
                    });
                  });
                },
              ),
            Visibility(
              visible: !isSelectorHidden,
              child: AnimatedOpacity(
                opacity: (link == null || virtualLoading) ? 1 : 0,
                duration: const Duration(milliseconds: 500),
                onEnd: () {
                  setState(() {
                    isSelectorHidden = true;
                  });
                },
                child: Container(
                  color: AppColor.white,
                  child: _VirtualUserSelector(
                    partner: widget.partner,
                    onLinkCreated: (link) {
                      setState(() {
                        virtualLink = link;
                      });
                    },
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class _VirtualUserSelector extends StatefulWidget {
  final Function(String? link) onLinkCreated;
  final String? partner;

  const _VirtualUserSelector(
      {required this.onLinkCreated, required this.partner});

  @override
  _VirtualUserSelectorState createState() => _VirtualUserSelectorState();
}

class _VirtualUserSelectorState extends State<_VirtualUserSelector> {
  int selectedUserIndex = GuardianController().currentGuardianIndex;

  bool isLoading = false;

  Future<bool> checkGuardian() async {
    if (!GuardianController().hasGuardian.value) {
      onSubmit();
      return true;
    }
    return false;
  }

  Future<void> onSubmit() async {
    if (isLoading) return;
    try {
      setState(() {
        isLoading = true;
      });
      String? resultLink;
      if (selectedUserIndex < 0) {
        UserApp userApp = Get.find<UserApp>();
        CustomerModel customer =
            userApp.customerData.value.customer ?? CustomerModel();
        resultLink = await getVirtualLink(
          isGuardian: false,
          guardianId: 0,
          firstName: customer.firstName ?? "",
          lastName: customer.lastName ?? "",
          gender: customer.gender ?? "",
          birthdate: customer.birthDate ?? "",
          passportId: customer.passportId ?? "",
          partner: widget.partner,
        );
      } else {
        final selectedUser =
            GuardianController().guardianAndMe.elementAt(selectedUserIndex);
        final isGuardian = selectedUserIndex != 0;
        if (isGuardian) {
          resultLink = await getVirtualLink(
            isGuardian: true,
            guardianId: selectedUser.id,
            firstName: selectedUser.firstName,
            lastName: selectedUser.lastName,
          );
        } else {
          UserApp userApp = Get.find<UserApp>();
          CustomerModel customer =
              userApp.customerData.value.customer ?? CustomerModel();
          resultLink = await getVirtualLink(
            isGuardian: false,
            guardianId: 0,
            firstName: selectedUser.firstName,
            lastName: selectedUser.lastName,
            gender: customer.gender ?? "",
            birthdate: customer.birthDate ?? "",
            passportId: customer.passportId ?? "",
            partner: widget.partner,
          );
        }
      }

      widget.onLinkCreated(resultLink);
    } catch (e) {
      print(e);
    }
  }

  Future<String?> getVirtualLink({
    String firstName = "",
    String lastName = "",
    String gender = "",
    String birthdate = "",
    String passportId = "",
    String? partner,
    required bool isGuardian,
    required int guardianId,
  }) async {
    try {
      final ApiClient client = ApiClient();
      AuthApp authApp = Get.find<AuthApp>();
      UserApp userApp = Get.find<UserApp>();
      CustomerModel? customer = userApp.customerData.value.customer;
      Map<String, dynamic> data = {
        'FirstName': firstName,
        'LastName': lastName,
        'Gender': gender,
        'DateOfBirth': birthdate,
        'NationalId': passportId,
        'IsGuardian': isGuardian,
        'GuardianId': guardianId,
        'Email': userApp.username.value,
        'PhoneNumber': (customer != null && customer.phoneNumber != null)
            ? customer.phoneNumber!
            : '',
        'idToken': authApp.cognitoIdToken.value,
        'Partner': partner
      };
      final ResponseModel response = await client.post(
        ApiConstant.virtualDomain,
        data: data,
      );

      if (response.data != null) {
        final VirtualModel virtualModel = VirtualModel.fromJson(response.data!);
        if (virtualModel.data != null) {
          return virtualModel.data!.serviceUrl;
        }
      }
      throw const VirtualMalformedResponseException();
    } catch (e) {
      if (e is VirtualMalformedResponseException) {
        await Get.dialog<void>(
          CustomAlertDialog(
            title: 'Error',
            description:
                "Virtual Hospital service has a problem right now. Please try again later or contact us.",
            onButtonPressed: () async {
              Get.back<void>();
            },
            suggestChat: true,
          ),
        );
      } else {
        await Get.dialog<void>(
          CustomAlertDialog(
            title: 'Error',
            description: "There is something wrong. Please try again later",
            onButtonPressed: () async {
              Get.back<void>();
            },
          ),
        );
      }
      Get.back<void>();
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        FutureBuilder(
          future: checkGuardian(),
          builder: (context, snapshot) {
            return const SizedBox.shrink();
          },
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 15, bottom: 40),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 500),
                        width: isLoading ? 50.w : 35.w,
                        child: Image.asset(
                          AppAsset.smtvLogo,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: isLoading ? 0 : 1,
                      child: AnimatedCrossFade(
                        firstChild: ConstrainedBox(
                          constraints: BoxConstraints(maxHeight: 70.h),
                          child: Column(
                            children: [
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 5),
                                  child: Text(
                                    AppLocalizations.of(context)!
                                        .guardianVirtualSelected,
                                    style: AppText.theme(context).titleSmall,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 20),
                              Expanded(
                                child: const GuardianSelector().guardianList(
                                  selectedUserIndex,
                                  onChanged: (value) {
                                    setState(() {
                                      selectedUserIndex = value ?? 0;
                                    });
                                  },
                                ),
                              )
                            ],
                          ),
                        ),
                        firstCurve: Curves.easeIn,
                        secondChild: Image.asset(
                          AppAsset.loadingAnimation,
                          fit: BoxFit.fitWidth,
                          height: 55.w,
                        ),
                        crossFadeState: (isLoading)
                            ? CrossFadeState.showSecond
                            : CrossFadeState.showFirst,
                        duration: const Duration(milliseconds: 500),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              Visibility(
                visible: GuardianController().hasGuardian.value,
                child: FilledButtonWidget(
                  enable: !isLoading,
                  label: AppTranslateKey.next,
                  onPressed: onSubmit,
                ),
              )
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: CustomCircleButton(
            onPressed: () => Get.back<void>(),
          ),
        )
      ],
    );
  }
}

class VirtualMalformedResponseException implements Exception {
  const VirtualMalformedResponseException();
}
