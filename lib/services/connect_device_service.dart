import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/screen/setting/connect_device/utils_health_app.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/plugin/health.dart';
import 'package:samitivej_flutter_app/utils/exception_handle.dart';
import 'package:samitivej_flutter_app/utils/extensions/date_format.dart';

enum AppState {
  DATA_NOT_FETCHED,
  FETCHING_DATA,
  DATA_READY,
  NO_DATA,
  AUTHORIZED,
  AUTH_NOT_GRANTED,
  DATA_ADDED,
  DATA_DELETED,
  DATA_NOT_ADDED,
  DATA_NOT_DELETED,
  STEPS_READY,
  HEALTH_CONNECT_STATUS,
  PERMISSIONS_REVOKING,
  PERMISSIONS_REVOKED,
  PERMISSIONS_NOT_REVOKED,
}

class ConnectDeviceService extends GetxController {
  static final _client = ApiClient();

  ConnectDeviceService._privateConstructor() {
    init();
  }

  static final ConnectDeviceService _instance =
      ConnectDeviceService._privateConstructor();

  factory ConnectDeviceService() {
    return _instance;
  }

  static var logger = Logger();
  GetStorage box = GetStorage();

  void init() async {
    Health().configure();
    authApp = Get.find<AuthApp>();
    _getId().then((value) => {
          if (value != null) {uuid = value}
        });

    // setEnable(isHaveData.value);
  }

  String latest_datetime = "";

  connectDevice() {
    getLastSync().then((v) {
      try {
        latest_datetime = v["latest_datetime"];
      } catch (e) {}
      if (isEnable()) {
        if (Platform.isAndroid) {
          getHealthConnectSdkStatus();
        } else {
          authorize();
        }
      }
    });
  }

  isEnable() {
    var r = box.read("health_connect") ?? false;
    print("isEnable   " + r.toString());
    return r == true;
  }

  setEnable(bool enable) {
    box.write("health_connect", enable);
  }

  static bool isHaveData = false;
  static final RxBool _isRefresh = false.obs;

  RxBool isRefresh() {
    return _isRefresh;
  }

  setRefresh(bool enable) {
    _isRefresh.value = enable;
  }

  final Rxn<AppState> _state = Rxn(AppState.DATA_NOT_FETCHED);

  Rxn<AppState> getState() {
    return _state;
  }

  // List<HealthDataPoint> _healthDataList = [];
  int _nofSteps = 0;
  List<RecordingMethod> recordingMethodsToFilter = [];

  // All types available depending on platform (iOS ot Android).
  List<HealthDataType> get types => (Platform.isAndroid)
      ? dataTypesAndroid
      : (Platform.isIOS)
          ? dataTypesIOS
          : [];

  List<HealthDataAccess> get permissions => types
      .map((type) =>
          // can only request READ permissions to the following list of types on iOS
          [
            HealthDataType.WALKING_HEART_RATE,
            HealthDataType.ELECTROCARDIOGRAM,
            // HealthDataType.HIGH_HEART_RATE_EVENT,
            // HealthDataType.LOW_HEART_RATE_EVENT,
            // HealthDataType.IRREGULAR_HEART_RATE_EVENT,
            HealthDataType.EXERCISE_TIME,
            HealthDataType.HEART_RATE,
            HealthDataType.RESTING_HEART_RATE,
            HealthDataType.HEART_RATE_VARIABILITY_RMSSD,
            HealthDataType.HEART_RATE_VARIABILITY_SDNN,
            HealthDataType.SLEEP_IN_BED,
            HealthDataType.SLEEP_ASLEEP,
            HealthDataType.SLEEP_AWAKE,
            HealthDataType.VO2_MAX,
            HealthDataType.ATRIAL_FIBRILLATION_BURDEN,
            HealthDataType.BLOOD_OXYGEN,
          ].contains(type)
              ? HealthDataAccess.READ
              : HealthDataAccess.READ_WRITE)
      .toList();

  // List<HealthDataType> typeRead = [
  // HealthDataType.ATRIAL_FIBRILLATION_BURDEN,
  // ];

  var uuid = "";
  AuthApp? authApp;

  Future<void> installHealthConnect() async {
    await Health().installHealthConnect();
  }

  /// Authorize, i.e. get permissions to access relevant health data.
  Future<void> authorize() async {
    // If we are trying to read Step Count, Workout, Sleep or other data that requires
    // the ACTIVITY_RECOGNITION permission, we need to request the permission first.
    // This requires a special request authorization call.
    //
    // The location permission is requested for Workouts using the Distance information.
    // types.add(HealthDataType.ATRIAL_FIBRILLATION_BURDEN);
    // permissions.add(HealthDataAccess.READ);

    await Permission.activityRecognition.request();
    await Permission.location.request();

    // Check if we have health permissions
    bool? hasPermissions =
        await Health().hasPermissions(types, permissions: permissions);

    // hasPermissions = false because the hasPermission cannot disclose if WRITE access exists.
    // Hence, we have to request with WRITE as well.
    hasPermissions = false;

    bool authorized = false;
    if (!hasPermissions) {
      // requesting access to the data types before reading them
      try {
        authorized = await Health()
            .requestAuthorization(types, permissions: permissions);
      } catch (error) {
        debugPrint("Exception in authorize: $error");
      }
    }

    _state.value =
        (authorized) ? AppState.AUTHORIZED : AppState.AUTH_NOT_GRANTED;
    // print("await Health" + _state.value.toString());
    if (_state.value == AppState.AUTHORIZED) {
      fetchData();
      fetchStepData();
    }
  }

  /// Gets the Health Connect status on Android.
  Future<void> getHealthConnectSdkStatus() async {
    assert(Platform.isAndroid, "This is only available on Android");

    final sdkStatus = await Health().getHealthConnectSdkStatus();
    if (sdkStatus != null) {
      bool status = await Health().isHealthConnectAvailable();
      if (status) {
        authorize();
      }
    } else {
      await Health().installHealthConnect();
    }
  }

  Future<String?> _getId() async {
    var deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      // import 'dart:io'
      var iosDeviceInfo = await deviceInfo.iosInfo;
      return iosDeviceInfo.identifierForVendor
          ?.toLowerCase(); // unique ID on iOS
    } else if (Platform.isAndroid) {
      var androidDeviceInfo = await deviceInfo.androidInfo;
      return androidDeviceInfo.id.toLowerCase();
    }
  }

  /// Fetch data points from the health plugin and show them in the app.
  Future<void> fetchData() async {
    final now = DateTime.now();
    DateTime tempDate =
        now.subtract(const Duration(days: 6, hours: 22, minutes: 59));
    // DateTime tempDate = now.copyWith(hour: 00,minute: 00);
    try {
      tempDate = DateFormat("yyyy-MM-ddTHH:mm:ss")
          .parse(latest_datetime)
          .subtract(const Duration(days: 0, hours: 22, minutes: 59));
    } catch (e) {}
    final yesterday = tempDate;

    // _healthDataList.clear();

    try {
      List<HealthDataPoint> healthData = await Health().getHealthDataFromTypes(
        types: types,
        startTime: yesterday,
        endTime: now,
        recordingMethodsToFilter: recordingMethodsToFilter,
      );
      healthData.sort((a, b) => b.dateTo.compareTo(a.dateTo));
      // List<HealthDataPoint> healthData2 = await Health().getHealthDataFromTypes(
      //   types: typeRead,
      //   startTime: yesterday,
      //   endTime: now,
      //   recordingMethodsToFilter: recordingMethodsToFilter,
      // );
      // healthData2.sort((a, b) => b.dateTo.compareTo(a.dateTo));

      postData(healthData);
      // postData(healthData2);
      // _healthDataList.addAll((healthData.length < 100) ? healthData : healthData.sublist(0, 100));

      if (Platform.isAndroid) {
        List<HealthDataPoint> totalBurn = await Health()
            .getHealthAggregateDataFromTypes(
                types: HealthDataType.TOTAL_CALORIES_BURNED,
                startDate: yesterday.setTime(setHour: 0, setMinute: 0),
                endDate: DateTime.now(),
                interval: ((60 * 24) * 60));
        totalBurn.sort((a, b) => b.dateTo.compareTo(a.dateTo));
        // for (var i in totalBurn) {
        //   printLongString(i.toString());
        // }

        String? tokenAA = authApp?.cognitoIdToken.value;
        ConnectDeviceService.nutrition(totalBurn, tokenAA, true);
      }
    } catch (error) {
      debugPrint("Exception in getHealthDataFromTypes: $error");
    }
    // _healthDataList = Health().removeDuplicates(_healthDataList);
  }

  static void printLongString(String text) {
    final RegExp pattern = RegExp('.{1,800}'); // 800 is the size of each chunk
    pattern
        .allMatches(text)
        .forEach((RegExpMatch match) => print(match.group(0)));
  }

  postData(List<HealthDataPoint> healthData) async {
    // printLongString("postData  ");
    // for (var i in healthData) {
    // if (!i.typeString.contains("SLEEP") && !i.typeString.contains("HEART") && !i.typeString.contains("DISTANCE_WALKING_RUNNING") && !i.typeString.contains("STEPS")) {
    // if (i.typeString.contains("OXYGEN")) {
    //   printLongString("DATA  "+i.toString());
    // }
    // if (i.typeString.contains("ATRIAL_FIBRILLATION_BURDEN")) {
    //   printLongString("ATRIAL_FIBRILLATION_BURDEN  "+i.toString());
    // }
    // }
    // print("tokenId      " + authApp!.tokenId.toString());
    String? tokenAA = authApp?.cognitoIdToken.value;
    ConnectDeviceService.sleep(healthData, tokenAA);
    ConnectDeviceService.nutrition(healthData, tokenAA, false);
    ConnectDeviceService.hearth(healthData, tokenAA);
    ConnectDeviceService.sport(healthData, tokenAA);
    ConnectDeviceService.emotion(healthData, tokenAA);
    // printLongString("postData");
  }

  Future<void> fetchStepData() async {
    int? steps;

    // get steps for today (i.e., since midnight)
    final now = DateTime.now();
    final midnight = DateTime(now.year, now.month, now.day);

    bool stepsPermission =
        await Health().hasPermissions([HealthDataType.STEPS]) ?? false;
    if (!stepsPermission) {
      stepsPermission =
          await Health().requestAuthorization([HealthDataType.STEPS]);
    }

    if (stepsPermission) {
      try {
        steps = await Health().getTotalStepsInInterval(midnight, now,
            includeManualEntry:
                !recordingMethodsToFilter.contains(RecordingMethod.manual));
      } catch (error) {
        debugPrint("Exception in getTotalStepsInInterval: $error");
      }
      _nofSteps = (steps == null) ? 0 : steps;
    } else {
      debugPrint("Authorization not granted - error in authorization");
      // setState(() => _state = AppState.DATA_NOT_FETCHED);
    }
  }

  Future<void> revokeAccess() async {
    _state.value = AppState.PERMISSIONS_REVOKING;

    bool success = false;

    try {
      await Health().revokePermissions();
      success = true;
    } catch (error) {
      debugPrint("Exception in revokeAccess: $error");
    }

    _state.value = success
        ? AppState.PERMISSIONS_REVOKED
        : AppState.PERMISSIONS_NOT_REVOKED;
  }

  static Future<String?> getDeviceId() async {
    String? deviceId;
    final deviceInfoPlugin = DeviceInfoPlugin();
    final deviceInfo = await deviceInfoPlugin.deviceInfo;

    if (deviceInfo is IosDeviceInfo) {
      deviceId = deviceInfo.identifierForVendor;
    } else if (deviceInfo is AndroidDeviceInfo) {
      deviceId = deviceInfo.id;
    }
    return deviceId;
  }

  static double? getByKeyHeart(HealthDataPoint healthData) {
    var a = healthData.value.toJson()["numericValue"];
    if (a is int) {
      return a.toDouble();
    } else if (a is double) {
      return a;
    } else if (a is String) {
      return double.parse(a);
    } else {
      return 0;
    }
    return 0;
  }

  static int? getByKeyHeartVo2Max(HealthDataPoint healthData) {
    var a = healthData.value.toJson()["numericValue"];
    if (a is int) {
      return a;
    } else if (a is double) {
      return a.toInt();
    } else if (a is String) {
      return int.parse(a);
    } else {
      return 0;
    }
    return 0;
  }

  static int? getByKeyWorkout(HealthDataPoint healthData) {
    return healthData.dateTo.difference(healthData.dateFrom).inMinutes.toInt();
  }

  static int? getByKey(List<HealthDataPoint> healthData, String key) {
    var sumSAS = 0;
    var total = 0;
    for (var element in healthData) {
      // print("${element.type}   ${element.typeString}  ${element.value.toJson()["numeric_value"]}");
      if (key == element.typeString) {
        total++;
        dynamic a;
        try {
          a = element.value.toJson()["numericValue"];
        } catch (e) {
          a = null;
        }
        // print( "  ${key} " + element.value.toJson().toString());
        // var a = element.value.toJson()["numericValue"];
        // print( "  ${key} " + a.toString());
        if (a is int) {
          sumSAS = sumSAS + a;
        } else if (a is double) {
          sumSAS = sumSAS + a.toInt();
        } else if (a is String) {
          sumSAS = sumSAS + int.parse(a);
        } else {
          sumSAS = sumSAS + 0;
        }
      }
    }
    // print(sumSAS.toString() + "  ${key} " + total.toString());
    if (sumSAS != 0 && total != 0) {
      return (sumSAS / total).toInt();
    }
    return 0;
  }

  static int? getByKeySleep(HealthDataPoint healthData) {
    var a = healthData.value.toJson()["numericValue"];
    // print( "  ${key} " + a.toString());
    if (a is int) {
      return a;
    } else if (a is double) {
      return a.toInt();
    } else if (a is String) {
      return int.parse(a);
    } else {
      return 0;
    }
  }

  static int? getByKeySport(List<HealthDataPoint> healthData, String key) {
    for (var element in healthData) {
      if (key == element.typeString) {
        var a = element.value.toJson()["workoutActivityType"];
        if (a is int) {
          return a;
        } else if (a is double) {
          return a.toInt();
        } else if (a is String) {
          return int.parse(a);
        } else {
          return 0;
        }
      }
    }
    return 0;
  }

  static String? getDateFrom(List<HealthDataPoint> healthData, String key) {
    var a = DateTime.now().toString().replaceAll(" ", "T") + "Z";
    for (var element in healthData) {
      // print("${element.type}   ${element.typeString}  ${element.value.toJson()["numeric_value"]}");
      if (element.typeString.contains(key)) {
        a = element.dateFrom.toString().replaceAll(" ", "T") + "Z";
      }
    }
    return a;
  }

  static String? getDateFromSleep(
      List<HealthDataPoint> healthData, String key) {
    for (var element in healthData) {
      // print("${element.type}   ${element.typeString}  ${element.value.toJson()["numeric_value"]}");
      if (element.typeString.contains(key)) {
        return element.dateFrom.toString().replaceAll(" ", "T") + "Z";
      }
    }
    return DateTime.now().toString().replaceAll(" ", "T") + "Z";
  }

  static String? getDateTo(List<HealthDataPoint> healthData, String key) {
    for (var element in healthData) {
      // print("${element.type}   ${element.typeString}  ${element.value.toJson()["numeric_value"]}");
      if (element.typeString.contains(key)) {
        return element.dateTo.toString().replaceAll(" ", "T") + "Z";
      }
    }
    return DateTime.now().toString().replaceAll(" ", "T") + "Z";
  }

  static int getByKeyInBed(List<HealthDataPoint> healthData) {
    int total = 0;
    for (var element in healthData) {
      // print("${element.type}   ${element.typeString}  ${element.value.toJson()["numeric_value"]}");
      if (element.typeString.contains("SLEEP")) {
        var a = element.value.toJson()["numericValue"];
        if (a is int) {
          total += a;
        } else if (a is double) {
          total += a.toInt();
        } else if (a is String) {
          total += int.parse(a);
        }
      }
    }
    return total;
  }

  static String durationToString(int? minutes) {
    if (minutes == null) {
      return "00:00:00";
    }
    var d = Duration(minutes: minutes);
    List<String> parts = d.toString().split(':');
    return '${parts[0].padLeft(2, '0')}:${parts[1].padLeft(2, '0')}:00'.trim();
  }

  static int convertToInt(a) {
    if (a is int) {
      return a;
    } else if (a is double) {
      return a.toInt();
    } else if (a is String) {
      return int.parse(a);
    } else {
      return 0;
    }
  }

  static Future<void> sleep(
      List<HealthDataPoint> healthData, String? token) async {
    UserApp userApp = Get.find<UserApp>();
    // final AuthApp authApp = Get.find<AuthApp>();
    final deviceId = await getDeviceId();
    // print("sleep    "+token.toString());
    var dataJsonArray = [];

    for (var i in healthData) {
      if (i.typeString.contains("SLEEP") ||
          i.typeString.contains("BLOOD_OXYGEN")) {
        var dataJson = {
          "user_id": userApp.userInfo?.value.uuid ?? deviceId,
          "total_sleep_time_in_bed": "00:00:00",
          "time_in_bed": "00:00:00",
          "awake_more_than_20_min": "00:00:00",
          "sleep_quality": 0,
          "o2_saturation_during_sleep": 0,
          "rhr_during_sleep": 0,
          "o2_desaturation": 0,
          "sleep_light": "00:00:00",
          "sleep_deep": "00:00:00",
          "sleep_rem": "00:00:00",
          "sleep_awake": "00:00:00",
          "token_id": token,
          // "dateFrom": 0,
          // "dateTo": 0,
        };
        if (i.typeString == "SLEEP_SESSION") {
          dataJson["total_sleep_time_in_bed"] =
              durationToString(getByKeySleep(i));
        } else if (i.typeString == "SLEEP_AWAKE_IN_BED") {
          dataJson["awake_more_than_20_min"] =
              durationToString(getByKeySleep(i));
        } else if (i.typeString == "SLEEP_LIGHT") {
          dataJson["sleep_light"] = durationToString(getByKeySleep(i));
        } else if (i.typeString == "SLEEP_DEEP") {
          dataJson["sleep_deep"] = durationToString(getByKeySleep(i));
        } else if (i.typeString == "SLEEP_REM") {
          dataJson["sleep_rem"] = durationToString(getByKeySleep(i));
        } else if (i.typeString == "SLEEP_AWAKE") {
          dataJson["sleep_awake"] = durationToString(getByKeySleep(i));
        } else if (i.typeString == "SLEEP_ASLEEP") {
          dataJson["time_in_bed"] = durationToString(getByKeySleep(i));
        } else if (i.typeString == "SLEEP_IN_BED") {
          dataJson["total_sleep_time_in_bed"] =
              durationToString(getByKeySleep(i));
        } else if (i.typeString == "BLOOD_OXYGEN") {
          double a = getByKeyHeart(i) ?? 0;
          if (Platform.isAndroid) {
            dataJson["o2_saturation_during_sleep"] = a;
          } else {
            dataJson["o2_saturation_during_sleep"] = a * 100;
          }
        }

        dataJson["dateFrom"] = "${i.dateFrom.toString().replaceAll(" ", "T")}Z";
        dataJson["dateTo"] = "${i.dateTo.toString().replaceAll(" ", "T")}Z";
        dataJsonArray.add(dataJson);
      }
    }
    var jsonBody = jsonEncode(dataJsonArray);
    // printLongString("sleepsleepsleep    " + jsonBody);
    sleepApi(dataJsonArray);
  }

  static Future<void> sleepApi(var dataJson) async {
    try {
      // print("getDateFrom   " + getDateFrom(healthData, "SLEEP").toString());
      // print("getDateTo  " + getDateTo(healthData, "SLEEP").toString());

      final resp = await _client.post(
        ApiConstant.sleep,
        data: dataJson,
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        _isRefresh.value = true;
        // print(rawResp.toString());
        return;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<void> nutrition(List<HealthDataPoint> healthData, String? token,
      bool is_energy_total) async {
    UserApp userApp = Get.find<UserApp>();

    // var token = await authApp.getTotalHealthToken();
    final deviceId = await getDeviceId();

    var dataJsonArray = [];
    double total = 0.0;
    for (var i in healthData) {
      // print(i.typeString);
      if (i.typeString == "WEIGHT" ||
          i.typeString == "HEIGHT" ||
          i.typeString == "STEPS" ||
          i.typeString == "EXERCISE_TIME" ||
          i.typeString == "TOTAL_CALORIES_BURNED" ||
          i.typeString == "ACTIVE_ENERGY_BURNED" ||
          i.typeString == "BASAL_ENERGY_BURNED" ||
          i.typeString == "WORKOUT") {
        var dataJson = {
          "user_id": userApp.userInfo?.value.uuid ?? deviceId,
          "weight": getByKey(healthData, "WEIGHT"),
          "height": getByKey(healthData, "HEIGHT"),
          "step_count": 0,
          "exercise_time": 0,
          "burn_cal": 0,
          "token_id": token,
          "is_energy_total": is_energy_total,
          // "dateFrom": getDateFrom(healthData, "STEPS"),
          // "dateTo": getDateTo(healthData, "STEPS"),
        };
        if (i.typeString == "WEIGHT") {
          dataJson["weight"] = getByKeyHeart(i);
        } else if (i.typeString == "HEIGHT") {
          dataJson["height"] = getByKeyHeart(i);
        } else if (i.typeString == "STEPS") {
          var a = getByKeyHeart(i);
          if (Platform.isIOS) {
            if (i.sourceName.contains("Watch")) {
              total += a!;
              dataJson["step_count"] = getByKeyHeart(i);
            }
          } else {
            total += a!;
            dataJson["step_count"] = getByKeyHeart(i);
          }
        } else if (i.typeString == "WORKOUT" ||
            i.typeString == "EXERCISE_TIME") {
          dataJson["exercise_time"] = getByKeyWorkout(i);
        } else if (i.typeString == "TOTAL_CALORIES_BURNED") {
          isHaveData = true;
          dataJson["burn_cal"] = getByKeyHeart(i);
        } else if (i.typeString == "ACTIVE_ENERGY_BURNED") {
          isHaveData = true;
          double a = getByKeyHeart(i) ?? 0.0;
          String inString = a.toString().split(".")[0];
          String inString2 = a.toString().split(".")[1].substring(0, 1);
          dataJson["activeEnergyBurned"] = double.parse("$inString.$inString2");
        } else if (i.typeString == "BASAL_ENERGY_BURNED") {
          double a = getByKeyHeart(i) ?? 0.0;
          String inString = a.toString().split(".")[0];
          String inString2 = a.toString().split(".")[1].substring(0, 1);
          dataJson["basalEnergyBurned"] = double.parse("$inString.$inString2");
        }
        dataJson["dateFrom"] = "${i.dateFrom.toString().replaceAll(" ", "T")}Z";
        dataJson["dateTo"] = "${i.dateTo.toString().replaceAll(" ", "T")}Z";
        dataJsonArray.add(dataJson);
      }
    }

    var jsonBody = jsonEncode(dataJsonArray);
    printLongString("nutrition      " + jsonBody);
    try {
      final resp = await _client.post(
        ApiConstant.nutrition,
        data: dataJsonArray,
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        _isRefresh.value = true;
        // print(rawResp.toString());
        return;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<void> hearth(
      List<HealthDataPoint> healthData, String? token) async {
    UserApp userApp = Get.find<UserApp>();
    // final AuthApp authApp = Get.find<AuthApp>();
    // var token = await authApp.getTotalHealthToken();
    final deviceId = await getDeviceId();
    var dataJsonArray = [];

    for (var i in healthData) {
      // print(i.typeString);
      if (i.typeString.contains("HEART") ||
          i.typeString.contains("ATRIAL_FIBRILLATION_BURDEN")) {
        var dataJson = {
          "user_id": userApp.userInfo?.value.uuid ?? deviceId,
          "resting_heart_rate": 0,
          "average_heart_rate": 0,
          "arrhythmia_status": "Normal",
          // "cardio_swing": 0,
          // "recovery_score": 0,
          "token_id": token,
          // "dateFrom": getDateFrom(healthData, "HEART_RATE"),
          // "dateTo": getDateTo(healthData, "HEART_RATE"),
        };
        // print("asdasd");
        if (i.typeString == "HEART_RATE") {
          dataJson["average_heart_rate"] = getByKeyHeart(i);
        } else if (i.typeString == "RESTING_HEART_RATE") {
          dataJson["resting_heart_rate"] = getByKeyHeart(i);
        } else if (i.typeString == "ATRIAL_FIBRILLATION_BURDEN") {
          var a = getByKeyHeart(i);
          if (a != null) {
            dataJson["arrhythmia_status"] = "Abnormal";
          }
        }
        dataJson["dateFrom"] = "${i.dateFrom.toString().replaceAll(" ", "T")}Z";
        dataJson["dateTo"] = "${i.dateTo.toString().replaceAll(" ", "T")}Z";
        dataJsonArray.add(dataJson);
      }
    }
    // var jsonBody = jsonEncode(dataJsonArray);
    // printLongString(jsonBody);
    hearthApi(dataJsonArray);
  }

  // void printLongString(String text) {
  //   final RegExp pattern = RegExp('.{1,800}'); // 800 is the size of each chunk
  //   pattern.allMatches(text).forEach((RegExpMatch match) => print(match.group(0)));
  // }
  static Future<void> hearthApi(var dataJson) async {
    try {
      final resp = await _client.post(
        ApiConstant.hearth,
        data: dataJson,
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        _isRefresh.value = true;
        // print(rawResp.toString());
        return;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<void> sport(
      List<HealthDataPoint> healthData, String? token) async {
    UserApp userApp = Get.find<UserApp>();
    final deviceId = await getDeviceId();
    var dataJsonArray = [];
    double total = 0.0;

    for (var i in healthData) {
      if (i.typeString.contains("WORKOUT") ||
          i.typeString.contains("STEPS") ||
          i.typeString.contains("VO2_MAX") ||
          i.typeString == "EXERCISE_TIME") {
        var dataJson = {
          "user_id": userApp.userInfo?.value.uuid ?? deviceId,
          "age": 0,
          "gender": getByKey(healthData, "GENDER") == 0 ? "male" : "female",
          // "exercise_time": getByKeySport(healthData, "WORKOUT"),
          "step_count": 0,
          "step_goal_average": 0,
          "vO2max_score": 0,
          "activity_minite": 0,
          "token_id": token,
        };
        // print("asdasd");
        if (i.typeString == "STEPS") {
          var a = getByKeyHeart(i);
          if (Platform.isIOS) {
            if (i.sourceName.contains("Watch")) {
              total += a!;
              // print("STEPS " + a.toString());
              // print("total " + total.toString());
              dataJson["step_count"] = a;
            }
          } else {
            total += a!;
            // print("STEPS " + a.toString());
            // print("total " + total.toString());
            dataJson["step_count"] = a;
          }
        } else if (i.typeString == "WORKOUT" ||
            i.typeString == "EXERCISE_TIME") {
          dataJson["exercise_time"] = getByKeyWorkout(i);
        } else if (i.typeString == "VO2_MAX") {
          dataJson["vO2max_score"] = getByKeyHeartVo2Max(i);
        }
        dataJson["dateFrom"] = "${i.dateFrom.toString().replaceAll(" ", "T")}Z";
        dataJson["dateTo"] = "${i.dateTo.toString().replaceAll(" ", "T")}Z";
        dataJsonArray.add(dataJson);
      }
    }
    // var jsonBody = jsonEncode(dataJsonArray);
    // printLongString("step_counta   " + jsonBody);
    sportApi(dataJsonArray);
  }

  static Future<void> sportApi(var json) async {
    try {
      final resp = await _client.post(
        ApiConstant.sport,
        data: json,
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        _isRefresh.value = true;
        // print(rawResp.toString());
        return;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<void> emotion(
      List<HealthDataPoint> healthData, String? token) async {
    UserApp userApp = Get.find<UserApp>();
    // final AuthApp authApp = Get.find<AuthApp>();
    // var token = await authApp.getTotalHealthToken();
    final deviceId = await getDeviceId();
    var dataJsonArray = [];

    for (var i in healthData) {
      if (i.typeString.contains("HEART_RATE_VARIABILITY") ||
          i.typeString == "GENDER") {
        var dataJson = {
          "user_id": userApp.userInfo?.value.uuid ?? deviceId,
          "age": 0,
          "gender": "M",
          "hrv": 0,
          "sd": 0,
          "hrv_max": 0,
          "hrv_min": 0,
          "stress_score": 0,
          "token_id": token,
          "dateFrom": getDateFrom(healthData, "HEART_RATE_VARIABILITY"),
          "dateTo": getDateTo(healthData, "HEART_RATE_VARIABILITY"),
        };
        if (i.typeString.contains("HEART_RATE_VARIABILITY")) {
          dataJson["hrv"] = getByKeyHeart(i);
        } else if (i.typeString == "GENDER") {
          dataJson["gender"] = getByKeyHeart(i) == 0 ? "male" : "female";
        }
        dataJson["dateFrom"] = "${i.dateFrom.toString().replaceAll(" ", "T")}Z";
        dataJson["dateTo"] = "${i.dateTo.toString().replaceAll(" ", "T")}Z";
        dataJsonArray.add(dataJson);
      }
    }
    // var jsonBody = jsonEncode(dataJsonArray);
    // printLongString(jsonBody);
    emotionApi(dataJsonArray);
  }

  static Future<void> emotionApi(var json) async {
    try {
      final resp = await _client.post(
        ApiConstant.emotion,
        data: json,
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        _isRefresh.value = true;
        // print(rawResp.toString());
        return;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getOverView() async {
    try {
      var url = ApiConstant.senseScore;
      final resp = await _client.get(
        url,
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        return rawResp;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getHealthTracker() async {
    try {
      var url = "${ApiConstant.healthTracker}${authApp?.cognitoIdToken.value}";
      final resp = await _client.get(
        url,
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        return rawResp;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<bool> getNewCustomer() async {
    try {
      var url = "${ApiConstant.isNewCustomer}${authApp?.cognitoIdToken.value}";
      final resp = await _client.getRaw(
        url,
      );
      return resp;
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getLastSync() async {
    try {
      var url = "${ApiConstant.lastSync}${authApp?.cognitoIdToken.value}";
      final resp = await _client.get(
        url,
      );
      final rawResp = resp.data;
      if (rawResp != null) {
        return rawResp;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getCustomerTermWearable() async {
    try {
      var url = "${ApiConstant.checkCustomer}${authApp?.cognitoIdToken.value}";
      final resp = await _client.post(
        url,
      );
      final rawResp = resp.data;
      if (rawResp != null) {
        return rawResp;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> setAcceptTerm() async {
    try {
      var url = "${ApiConstant.acceptTerm}${authApp?.cognitoIdToken.value}";
      final resp = await _client.post(
        url,
      );
      final rawResp = resp.data;
      if (rawResp != null) {
        return rawResp;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getBmi() async {
    try {
      var url = ApiConstant.getBMI;
      final resp = await _client.post(
        url,
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        return rawResp;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }
}
