import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/apps/appointment/appointment_app.dart';
import 'package:samitivej_flutter_app/apps/appointment/my_appointment_app.dart';
import 'package:samitivej_flutter_app/apps/appointment/plus_appointment_app.dart';
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/apps/feature_flags/feature_flags_app.dart';
import 'package:samitivej_flutter_app/apps/health_app/health_app.dart';
import 'package:samitivej_flutter_app/apps/home/<USER>';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/commons/api/network_info.dart';
import 'package:samitivej_flutter_app/commons/di/app_injector.dart';
import 'package:samitivej_flutter_app/components/confirm_dialog.dart';
import 'package:samitivej_flutter_app/components/custom_alert_dialog.dart';
import 'package:samitivej_flutter_app/components/custom_circle_button.dart';
import 'package:samitivej_flutter_app/components/custom_scaffold.dart';
import 'package:samitivej_flutter_app/components/custom_webview.dart';
import 'package:samitivej_flutter_app/constants/analytics_constant.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/all_type_appointment_model.dart';
import 'package:samitivej_flutter_app/models/doctor_model.dart';
import 'package:samitivej_flutter_app/models/fit_arguments/fit_arguments.dart';
import 'package:samitivej_flutter_app/models/home_service_item_model.dart';
import 'package:samitivej_flutter_app/presentation/pages/samitivej_plus/plus_home_screen.dart';
import 'package:samitivej_flutter_app/presentation/pages/vaccine/vaccine_onboarding.dart';
import 'package:samitivej_flutter_app/presentation/widgets/dialog/no_internet_dialog.dart';
import 'package:samitivej_flutter_app/routes/app_pages.dart';
import 'package:samitivej_flutter_app/screen/appointment/appointment_detail_screen.dart';
import 'package:samitivej_flutter_app/screen/appointment/appointment_screen.dart';
import 'package:samitivej_flutter_app/screen/appointment/my_appointment_screen.dart';
import 'package:samitivej_flutter_app/screen/article/article_screen.dart';
import 'package:samitivej_flutter_app/screen/article/articles_screen.dart';
import 'package:samitivej_flutter_app/screen/cart/cart_webview_w_scaffold.dart';
import 'package:samitivej_flutter_app/screen/coins/coins_screen.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>';
import 'package:samitivej_flutter_app/screen/home_fit/controllers/home_fit_app.dart';
import 'package:samitivej_flutter_app/screen/home_fit/views/home_fit_view.dart';
import 'package:samitivej_flutter_app/screen/payment/e-coupon_webview_screen.dart';
import 'package:samitivej_flutter_app/screen/setting/connect_device/connect_device_screen.dart';
import 'package:samitivej_flutter_app/screen/setting/setting_child_screens/connect_device/term_screen.dart';
import 'package:samitivej_flutter_app/screen/setting/setting_child_screens/connect_hn_flow/connect_hn_wizard.dart';
import 'package:samitivej_flutter_app/screen/stack_bar/home_stack_bar.dart';
import 'package:samitivej_flutter_app/services/appointment/doctor_service.dart';
import 'package:samitivej_flutter_app/services/connect_device_service.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';
import 'package:samitivej_flutter_app/utils/permission_util.dart';
import 'package:url_launcher/url_launcher.dart';

enum WellServiceKey {
  samitivej,
  food,
  fit,
  health, // have parameter
  gene,
  mom,
  aidoctor,
  wearable,
  corp,
  medrefill,
  shop, // not used
  kid, // not used
  engage, // not used
  vaccine, // have parameter
}

enum WellDeepLink {
  payment, // have parameter
  article, // have parameter
  discover, // have parameter
  virtual,
  appointment,
  chatfood,
  chatfit,
  chathelp,
  chatmedrefill,
  challengefit, // have parameter
  coupon,
  coin,
  membership
}

enum WellLaunchUrlConfig {
  miniapp, // need parameter
  landing, // need parameter
  external, // need parameter
}

enum ChatModule {
  well,
  food,
  fit,
  medrefill,
}

class WellNavigationService {
  static bool handleRoute(String initialRoute) {
    if (initialRoute.trim().isEmpty) initialRoute = "/";
    final initialRouteToken = initialRoute.split("?");
    final rawQueryString =
        initialRouteToken.length > 1 ? initialRouteToken.removeLast() : "";
    final firstPath = initialRouteToken.join("").split("/").elementAt(1);

    final FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();

    final homeApp = Get.find<HomeApp>();
    final service = homeApp.serviceList.firstWhereOrNull(
      (element) => element.key == firstPath,
    );

    final WellDeepLink? deepLink = WellDeepLink.values
        .firstWhereOrNull((route) => route.name == firstPath);

    logUTM(rawQueryString);

    if (service != null) {
      FirebaseAnalytics.instance.logEvent(
        name: AnalyticsConstant.eventWellServices,
        parameters: {
          'service_name': "${service.key} via deep link",
        },
      );

      onSelectedWellService(service, queryString: rawQueryString);
      return true;
    } else if (deepLink != null) {
      switch (deepLink) {
        case WellDeepLink.payment:
          {
            if (featureFlagsApp.payment.isFalse) {
              return false;
            }
            final commonApp = Get.find<CommonApp>();
            FirebaseAnalytics.instance.logEvent(
              name: AnalyticsConstant.eventWellServices,
              parameters: {
                'service_name': "payment via deep link",
              },
            );
            commonApp.paymentQuery.value = rawQueryString;
            CartWebviewWithScaffold.open();
            return true;
          }
        case WellDeepLink.article:
        case WellDeepLink.discover:
          {
            if (featureFlagsApp.homeDiscovers.isFalse) {
              return false;
            }
            FirebaseAnalytics.instance.logEvent(
              name: AnalyticsConstant.eventWellServices,
              parameters: {
                'service_name': "article via deep link",
              },
            );
            final uri = WebUri(initialRoute);
            final articleId = int.tryParse(uri.queryParameters["id"] ?? "");
            if (articleId != null) {
              Get.to(ArticleScreen(
                id: articleId,
              ));
            } else {
              final selectedTag = uri.queryParameters["tag"] ?? "";
              final searchString = uri.queryParameters["search"] ?? "";
              if (searchString.isNotEmpty) {
                Get.to(ArticlesScreen(searchString: searchString));
              } else {
                Get.to(ArticlesScreen(selectedTag: selectedTag));
              }
            }
            return true;
          }
        case WellDeepLink.virtual:
          {
            if (featureFlagsApp.homeDoctorConsult.isTrue) {
              final uri = WebUri(initialRoute);
              final partner = uri.queryParameters["partner"];
              openVirtual(partner: partner);
            }
            return true;
          }
        case WellDeepLink.membership:
          {
            WebUri uri = WebUri(initialRoute);
            final query = uri.queryParameters;
            openMembership(id: query["id"]);
            return true;
          }
        case WellDeepLink.appointment:
          {
            final uri = WebUri(initialRoute);
            String? appointmentId = uri.queryParameters["appointmentId"];
            String? status = uri.queryParameters["status"];
            if (appointmentId != null && int.tryParse(appointmentId) != null) {
              if (featureFlagsApp.myAppointment.isTrue) {
                openMyAppointmentDetail(int.tryParse(appointmentId)!);
              }
            } else if (status != null) {
              status = status.toLowerCase();
              int tabIndex = 0;
              if (status.contains("confirm") || status.contains("change")) {
                tabIndex = 1;
              } else if (status.contains("finish")) {
                tabIndex = 2;
              } else if (status.contains("cancel")) {
                tabIndex = 3;
              }
              if (featureFlagsApp.myAppointment.isTrue) {
                openMyAppointment(tabIndex);
              }
            } else {
              if (featureFlagsApp.appointment.isTrue) {
                openAppointment();
              }
            }
            return true;
          }
        case WellDeepLink.chatfood:
          {
            openChat(module: ChatModule.food);
            return true;
          }
        case WellDeepLink.chatfit:
          {
            openChat(module: ChatModule.fit);
            return true;
          }
        case WellDeepLink.chathelp:
          {
            openChat();
            return true;
          }
        case WellDeepLink.chatmedrefill:
          {
            openChat(module: ChatModule.medrefill);
            return true;
          }
        case WellDeepLink.challengefit:
          {
            WebUri uri = WebUri(initialRoute);
            final query = uri.queryParameters;
            final rawChallengeId = query["challengeId"];
            if (rawChallengeId == null) return false;
            final challengeId = int.tryParse(rawChallengeId);
            if (challengeId == null) return false;
            openFitChallenge(challengeId);
            return true;
          }
        case WellDeepLink.coupon:
          {
            openCoupon();
            return true;
          }
        case WellDeepLink.coin:
          {
            openCoin();
            return true;
          }
      }
    } else {
      final WellLaunchUrlConfig? config = WellLaunchUrlConfig.values
          .firstWhereOrNull((mode) => mode.name == firstPath);
      if (config != null) {
        final Uri parsedUri = Uri.parse(initialRoute);
        final query = parsedUri.queryParameters;
        final dest = query["dest"];
        final title = query["title"];
        if (dest != null) {
          try {
            final decode = Uri.decodeComponent(dest);
            final decodedUri = WebUri(decode);
            if (decodedUri.isValidUri) {
              launchUrlWithConfig(
                decodedUri,
                config: config,
                title: title,
              );
              return true;
            } else {
              return false;
            }
          } catch (e) {
            return false;
          }
        } else {
          return false;
        }
      } else {
        return false;
      }
    }
  }

  static Future<bool> launchUrlWithConfig(
    Uri url, {
    WellLaunchUrlConfig config = WellLaunchUrlConfig.external,
    String? title,
  }) async {
    // miniapp (isinapp)
    // landing (isinapp + showappbar)
    // external (launch url)
    switch (config) {
      case WellLaunchUrlConfig.external:
        {
          final validUrl = await canLaunchUrl(url);
          if (validUrl) {
            launchUrl(url);
          }
          return validUrl;
        }
      case WellLaunchUrlConfig.landing:
        {
          CustomWebViewWithScaffold.launch(
            url: WebUri.uri(url),
            showAppBar: true,
            title: title,
            cookieWebUrl: null,
            icon: CustomScaffoldIcon.back,
          );
          return true;
        }
      case WellLaunchUrlConfig.miniapp:
        {
          CustomWebViewWithScaffold.launch(
            url: WebUri.uri(url),
            cookieWebUrl: null,
          );
          return true;
        }
    }
  }

  static Future<void> onSelectedWellService(
    HomeServiceItemModel serviceData, {
    String? queryString,
  }) async {
    final isConnected = await AppInjector.get<NetworkInfo>().isConnected;
    if (!isConnected) {
      NoInternetDialog.show(
        onRetry: () {
          onSelectedWellService(serviceData, queryString: queryString);
        },
      );
      return;
    }

    final FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();
    FirebaseAnalytics.instance.logEvent(
      name: AnalyticsConstant.eventWellServices,
      parameters: {
        'service_name': serviceData.key,
      },
    );
    AppsflyerSdk appsflyerSdk = AppsflyerSdk(AppsFlyerOptions(
      afDevKey: ApiConstant.appflyerKey,
      appId: ApiConstant.appflyerId,
      showDebug: true,
    ));
    bool is_accept_term = false;
    bool is_access_wearable = false;
    final serviceKey = serviceData.serviceKey;
    if (serviceKey != null) {
      switch (serviceKey) {
        case WellServiceKey.food:
          if (featureFlagsApp.miniAppFood.isFalse) {
            return;
          }
          await openFood(queryString: queryString);
          await appsflyerSdk.logEvent(
              "Food_interaction", {"af_param_1": "FoodComplete Registration"});
          await appsflyerSdk.logEvent(
              "InappPurchase", {"af_param_1": "FoodComplete Registration"});
          break;
        case WellServiceKey.fit:
          if (featureFlagsApp.miniAppFit.isFalse) {
            return;
          }
          await openFit();
          await appsflyerSdk.logEvent(
              "Fit_interaction", {"af_param_1": "FitComplete Registration"});
          await appsflyerSdk.logEvent(
              "InappPurchase", {"af_param_1": "FitComplete Registration"});
          break;
        case WellServiceKey.health:
          if (featureFlagsApp.miniAppHealthCheckup.isFalse) {
            return;
          }
          await openTotalHeathSolution(queryString: queryString);
          await appsflyerSdk.logEvent("Health_interaction",
              {"af_param_1": "HealthComplete Registration"});
          await appsflyerSdk.logEvent(
              "InappPurchase", {"af_param_1": "HealthComplete Registration"});
          break;
        case WellServiceKey.gene:
          if (featureFlagsApp.miniAppGene.isFalse) {
            return;
          }
          final urlFromUnleash = featureFlagsApp
              .getServiceInfo(WellServiceKey.gene.name)
              ?.redirectUrl;

          final url = Get.locale?.languageCode == 'th'
              ? ApiConstant.geneTestWebTHDomain
              : ApiConstant.geneTestWebENDomain;
          await CustomWebViewWithScaffold.launch(
            url: WebUri(urlFromUnleash ?? url),
            showAppBar: true,
            title: serviceData.name,
            service: 'gene',
            cookieWebUrl: urlFromUnleash ?? url,
          );
          break;
        case WellServiceKey.mom:
          if (featureFlagsApp.miniAppMom.isFalse) {
            return;
          }

          final urlFromUnleash = featureFlagsApp
              .getServiceInfo(WellServiceKey.mom.name)
              ?.redirectUrl;

          final url = (urlFromUnleash ?? ApiConstant.mitrmomWebDomain) +
              (queryString != null ? "?$queryString" : "");
          await CustomWebViewWithScaffold.launch(
            url: WebUri(url),
            service: 'mom',
            cookieWebUrl: url,
          );
          await appsflyerSdk.logEvent(
              "Mom_interaction", {"af_param_1": "MomComplete Registration"});
          await appsflyerSdk.logEvent(
              "InappPurchase", {"af_param_1": "MomComplete Registration"});
          break;
        case WellServiceKey.samitivej:
          if (featureFlagsApp.miniAppSamitivej.isFalse) {
            return;
          }
          await openSamitivej();
          break;
        case WellServiceKey.medrefill:
          if (featureFlagsApp.miniAppMedRefill.isFalse) {
            return;
          }
          final Uri parsedUri = Uri.parse("?$queryString");
          final query = parsedUri.queryParameters;
          final billNo = query["billNo"];
          await openMedRefill(billNo: billNo, queryString: queryString);
          await appsflyerSdk.logEvent("MedRefill_interaction",
              {"af_param_1": "MedRefillComplete Registration"});
          await appsflyerSdk.logEvent("InappPurchase",
              {"af_param_1": "MedRefillComplete Registration"});
          break;
        case WellServiceKey.vaccine:
          if (featureFlagsApp.miniAppVaccine.isFalse) {
            return;
          }
          await openVaccine(queryString: queryString);
          break;

        case WellServiceKey.aidoctor:
          if (featureFlagsApp.miniAppAiDoctor.isFalse) {
            return;
          }
          await appsflyerSdk.logEvent(
              "ai_doctor_interaction", {"af_param_1": "open_ai_doctor"});
          await openAIDoctor(queryString: queryString);
          break;

        case WellServiceKey.wearable:
          if (featureFlagsApp.miniAppWearable.isFalse) {
            return;
          }
          await appsflyerSdk.logEvent(
              "wearable_clinic_interaction", {"af_param_1": "open_wearable_clinic"});
          await openWearable(queryString: queryString);
          // ConnectDeviceService connectDeviceService =
          //     Get.find<ConnectDeviceService>();
          // CommonApp commonApp = Get.find<CommonApp>();
          // AuthApp authApp = Get.find<AuthApp>();
          // await appsflyerSdk.logEvent("wearable_clinic_interaction",
          //     {"af_param_1": "open_wearable_clinic"});

          // connectDeviceService.getCustomerTermWearable().then((v) => {
          //       is_accept_term = v["is_accept_term"] ?? false,
          //       is_access_wearable = v["is_access_wearable"] ?? false,
          //       checkAccess(serviceData, authApp, commonApp, is_accept_term,
          //           is_access_wearable, connectDeviceService)
          //     });
          break;
        case WellServiceKey.corp:
          if (featureFlagsApp.miniAppCorp.isFalse) {
            return;
          }

          final urlFromUnleash = featureFlagsApp
              .getServiceInfo(WellServiceKey.corp.name)
              ?.redirectUrl;

          await CustomWebViewWithScaffold.launch(
            url: WebUri(urlFromUnleash ?? ApiConstant.corpDomain),
            title: serviceData.name,
            service: WellServiceKey.corp.name,
            cookieWebUrl: urlFromUnleash ?? ApiConstant.corpDomain,
          );
          break;
        case WellServiceKey.shop:
        case WellServiceKey.kid:
        case WellServiceKey.engage:
          if (serviceData.redirectUrl != null) {
            redirectHandler(
              redirectUrl: serviceData.redirectUrl!,
              isInApp: true,
              showAppBar: false,
            );
          }
          break;
      }
    } else {
      if (serviceData.redirectUrl != null) {
        redirectHandler(
          redirectUrl: serviceData.redirectUrl!,
          isInApp: true,
          showAppBar: false,
        );
      }
    }
  }

  static Future<void> openVirtual({String? partner}) async {
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();
    if (!featureFlagsApp.virtualHospital.value) {
      return;
    }
    CommonApp commonApp = Get.find<CommonApp>();
    final cameraResult = await commonApp.requestPermission(Permission.camera,
        forceOpenSetting: false);
    final micResult = await commonApp.requestPermission(Permission.microphone,
        forceOpenSetting: false);
    if (cameraResult && micResult) {
      await Get.to<void>(ConsultNowWebViewScreen(
        partner: partner,
      ));
    } else {
      Get.dialog<void>(
        CustomAlertDialog(
          title: 'Error',
          description:
              "Please grant permission on camera and microphone to continue.",
          buttonLabel: "Open settings",
          secondaryButtonLabel: AppTranslateKey.close,
          onButtonPressed: () async {
            await openAppSettings();
            Get.back<void>();
          },
          onSecondaryButtonPressed: () async {
            Get.back<void>();
          },
        ),
      );
    }
  }

  static Future<void> openSamitivej() async {
    UserApp userApp = Get.find<UserApp>();
    bool? isHNConnected = userApp.isHnConnected;
    isHNConnected ??= await Get.to<bool>(_WaitForHnScreen());

    if (isHNConnected == true) {
      Get.to<void>(const PlusHomeScreen());
    } else {
      final result = await Get.to<bool>(() => const ConnectHNWizard());
      if (result == true) {
        Get.to<void>(const PlusHomeScreen());
      }
    }
  }

  static Future<void> openAppointment() async {
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();

    final bool useAppointment = featureFlagsApp.appointment.value;
    if (!useAppointment) {
      return;
    }
    Get.to<void>(
      () => const AppointmentScreen(),
      binding: BindingsBuilder.put(() => AppointmentApp()),
    );
  }

  static Future<void> openMembership({String? id}) async {
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();

    final bool isEnabled = featureFlagsApp.membership.value;
    if (!isEnabled) {
      return;
    }

    UserApp userApp = Get.find<UserApp>();
    bool? isHNConnected = userApp.isHnConnected;

    isHNConnected ??= await Get.to<bool>(_WaitForHnScreen());

    if (isHNConnected == true) {
      CustomWebViewWithScaffold.launch(
        url: WebUri("${ApiConstant.paymentWebDomain}membership?id=$id"),
        showAppBar: false,
        cookieWebUrl: ApiConstant.paymentWebDomain,
      );
      return;
    } else {
      bool? connect = await Get.dialog<bool>(
        ConfirmDialog(
          message: AppTranslateKey.connectHNToUseThisService,
          cancelButtonLabel: AppTranslateKey.later,
          confirmButtonLabel: AppTranslateKey.connect,
          dismissable: false,
        ),
        barrierDismissible: false,
      );

      if (connect == true) {
        final result = await Get.to<bool>(() => const ConnectHNWizard());
        if (result == true) {
          CustomWebViewWithScaffold.launch(
            url: WebUri("${ApiConstant.paymentWebDomain}membership?id=$id"),
            showAppBar: false,
            cookieWebUrl: ApiConstant.paymentWebDomain,
          );
        }
      }
    }
  }

  static Future<void> openMyAppointment(int? tabIndex) async {
    await Get.to<void>(
      () => MyAppointmentScreen(
        tabIndex: tabIndex,
      ),
      binding: BindingsBuilder.put(() => MyAppointmentApp()),
    );
  }

  static Future<void> openMyAppointmentDetail(int appointmentId) async {
    final PlusAppointmentApp plusAppointmentApp =
        Get.find<PlusAppointmentApp>();

    bool appointmentIdExist() {
      return plusAppointmentApp.myAppointments.value.firstWhereOrNull(
              (app) => (app.appointmentId == appointmentId)) !=
          null;
    }

    Future<DoctorModel> getDoctor(AllTypeAppointmentModel appointment) async {
      final doctorDetail = await DoctorService.getDoctorDetail(
          site: appointment.site, doctorId: appointment.doctorId);
      final DoctorModel doctor = DoctorModel(
          drId: appointment.doctorId!,
          site: appointment.site!,
          fullNameTh: doctorDetail.fullNameTh,
          titleTh: doctorDetail.titleTh,
          fnameTh: doctorDetail.firstNameTh,
          lnameTh: doctorDetail.lastNameTh,
          fullNameEn: doctorDetail.fullNameEn,
          titleEn: doctorDetail.titleEn,
          fnameEn: doctorDetail.firstNameEn,
          lnameEn: doctorDetail.lastNameEn,
          image: doctorDetail.image,
          specialityTh: doctorDetail.specialityTh,
          specialityEn: doctorDetail.specialityEn,
          detail: doctorDetail,
          branchTh: doctorDetail.branchTh!.replaceAll('รพ.', 'โรงพยาบาล'),
          branchEn: '${doctorDetail.branchEn}',
          favorite: doctorDetail.favorite,
          gender: doctorDetail.sex);
      return doctor;
    }

    Get.to(const _WaitForAppointmentDetail());
    if (!appointmentIdExist()) {
      await plusAppointmentApp.getAllAppointments();
    }
    if (appointmentIdExist()) {
      final appointment = plusAppointmentApp.myAppointments.value
          .firstWhere((app) => (app.appointmentId == appointmentId));
      final doctor = await getDoctor(appointment);
      Get.off(
          () => AppointmentDetailScreen(
                appointment: appointment,
                appointmentType: AppointmentType.confirm,
                doctor: doctor,
                fromAppointmentScreen: false,
              ),
          binding: BindingsBuilder.put(() => MyAppointmentApp()));
    } else {
      await Get.dialog(CustomAlertDialog(
        title: AppTranslateKey.appointmentNotFound,
      ));
      Get.back();
    }
  }

  static Future<void> openChat({ChatModule module = ChatModule.well}) async {
    CommonApp commonApp = Get.find<CommonApp>();
    String chatLink = commonApp.chatWellDomainName;
    switch (module) {
      case ChatModule.well:
        chatLink = commonApp.chatWellDomainName;
        break;
      case ChatModule.food:
        chatLink = commonApp.chatWellDomainName;
        break;
      case ChatModule.fit:
        chatLink = commonApp.chatWellDomainName;
        break;
      case ChatModule.medrefill:
        chatLink = commonApp.chatWellDomainName;
        break;
    }
    CustomWebViewWithScaffold.launch(
      url: WebUri(chatLink),
      cookieWebUrl: chatLink,
    );
  }

  static wearableWeb2() async {
    AppsflyerSdk appsflyerSdk = AppsflyerSdk(AppsFlyerOptions(
      afDevKey: ApiConstant.appflyerKey,
      appId: ApiConstant.appflyerId,
      showDebug: true,
    ));
    await appsflyerSdk.logEvent(
        "wearable_clinic_interaction", {"af_param_1": "open_wearable_clinic"});
    CommonApp commonApp = Get.find<CommonApp>();
    AuthApp authApp = Get.find<AuthApp>();
    var url =
        "${ApiConstant.wearableDomain}app/overall?id_token=${authApp.cognitoIdToken.value}&refresh_token=${authApp.refreshToken}&lang=${commonApp.selectedLanguage.value}";
    CustomWebViewWithScaffold.launch(
      url: WebUri.uri(Uri.parse(url)),
      title: "Wearable",
      service: WellServiceKey.wearable.name,
      cookieWebUrl: ApiConstant.wearableDomain,
    );
  }

  static Future<void> openConnectDevice(bool isTerm) async {
    // Get.to<void>(ConnectDeviceHealth());
    if (isTerm) {
      var result = await Get.to<bool>(TermScreen());
      if (result == true) {
        Get.to<void>(ConnectDeviceHealth());
      }
    } else {
      Get.to<void>(ConnectDeviceHealth());
    }
  }

  static Future<void> openTotalHeathSolution({String? queryString}) async {
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();
    final urlFromUnleash =
        featureFlagsApp.getServiceInfo(WellServiceKey.health.name)?.redirectUrl;

    CommonApp commonApp = Get.find<CommonApp>();
    final linkWithQueryString =
        (urlFromUnleash ?? commonApp.totalHealthDomainName) +
            (queryString != null ? "?$queryString" : "");
    CustomWebViewWithScaffold.launch(
      url: WebUri(linkWithQueryString),
      service: "health",
      cookieWebUrl: commonApp.totalHealthDomainName,
    );
  }

  static Future<void> openFood({required String? queryString}) async {
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();
    final urlFromUnleash =
        featureFlagsApp.getServiceInfo(WellServiceKey.food.name)?.redirectUrl;

    CommonApp commonApp = Get.find<CommonApp>();
    HomeApp homeApp = Get.find<HomeApp>();
    CustomWebViewWithScaffold.launch(
      url: WebUri((urlFromUnleash ?? commonApp.foodDomainName) +
          (queryString != null ? "?$queryString" : "")),
      service: WellServiceKey.food.name,
      cookieWebUrl: urlFromUnleash ?? ApiConstant.foodWebDomain,
    );
    homeApp.getRegisteredFoodPlan();
  }

  static Future<void> openFit() async {
    HealthApp healthApp = Get.find<HealthApp>();
    CommonApp commonApp = Get.find<CommonApp>();
    HomeApp homeApp = Get.find<HomeApp>();
    healthApp.getDeviceInfo();
    commonApp.createCookie();
    (homeApp.isUserHaveFitPlan.value &&
            homeApp.dailyFitTask.isNotEmpty &&
            !homeApp.dailyFitTask.first.routineType!.contains("expire"))
        ? await Get.to<void>(() => const HomeFitView())
        : await homeApp.getFitDailyTask(
            startDate: DateTime(
                DateTime.now().year, DateTime.now().month, DateTime.now().day),
            endDate: DateTime(
                DateTime.now().year, DateTime.now().month, DateTime.now().day),
          );
    (homeApp.isUserHaveFitPlan.value &&
            homeApp.dailyFitTask.isNotEmpty &&
            !homeApp.dailyFitTask.first.routineType!.contains("expire"))
        ? await Get.to<void>(() => const HomeFitView())
        : await CustomWebViewWithScaffold.launch(
            url: WebUri(commonApp.fitQuestionDomainName),
            title: "Fit",
            service: "fit",
            cookieWebUrl: commonApp.fitQuestionDomainName,
          );
  }

  static Future<void> openMedRefill({
    String? billNo,
    required String? queryString,
  }) async {
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();
    final urlFromUnleash = featureFlagsApp
        .getServiceInfo(WellServiceKey.medrefill.name)
        ?.redirectUrl;

    String url = urlFromUnleash ?? ApiConstant.medRefillWebDomain;
    if (billNo != null) {
      url =
          '${urlFromUnleash ?? ApiConstant.medRefillWebDomain}orderdetail/$billNo';
    }
    url = url + (queryString != null ? "?$queryString" : "");
    await Permission.camera.request();
    await CustomWebViewWithScaffold.launch(
      url: WebUri(url),
      service: "medrefill",
      cookieWebUrl: url,
    );
  }

  static Future<void> openWearable({
    required String? queryString,
  }) async {
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();
    final urlFromUnleash = featureFlagsApp
        .getServiceInfo(WellServiceKey.wearable.name)
        ?.redirectUrl;

    String url = urlFromUnleash ??
        ApiConstant.wearableDomain +
            (queryString != null ? "?$queryString" : "");

    await CustomWebViewWithScaffold.launch(
      url: WebUri(url),
      service: "wearable",
      cookieWebUrl: url,
    );
    return;
  }

  static Future<void> openAIDoctor({
    required String? queryString,
  }) async {
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();
    final urlFromUnleash = featureFlagsApp
        .getServiceInfo(WellServiceKey.aidoctor.name)
        ?.redirectUrl;

    // UserApp userApp = Get.find<UserApp>();
    // String url = urlFromUnleash ?? ApiConstant.aidoctorDomain;
    // final customer = userApp.customerData.value.customer;
    // if (customer != null &&
    //     customer.username != null &&
    //     customer.username!.isNotEmpty) {
    //   final userId = customer.username!;
    //   final newUrl = url.replaceAll("user_id", userId);

    //   await CustomWebViewWithScaffold.launch(
    //     url: WebUri(url),
    //     service: "aidoctor",
    //     cookieWebUrl: newUrl,
    //   );
    //   return;
    // }
    String url = urlFromUnleash ??
        ApiConstant.aidoctorDomain +
            (queryString != null ? "?$queryString" : "");

    await CustomWebViewWithScaffold.launch(
      url: WebUri(url),
      service: "aidoctor",
      cookieWebUrl: url,
    );
    return;
  }

  static Future<void> openVaccine({
    required String? queryString,
  }) async {
    UserApp userApp = Get.find<UserApp>();
    bool? isHNConnected = userApp.isHnConnected;
    if (isHNConnected == null) {
      return;
    }

    bool used;
    final storage = StorageService();
    try {
      final bool? cookie = storage.read<bool>('vaccine-journey-onboarding');
      used = (cookie != null);
    } catch (e) {
      used = false;
    }
    if (!used) {
      final continued =
          await Get.to<bool>(() => const VaccineOnboardingScreen());
      if (continued == false) {
        return;
      }
    }
    FeatureFlagsApp featureFlagsApp = Get.find<FeatureFlagsApp>();
    final urlFromUnleash = featureFlagsApp
        .getServiceInfo(WellServiceKey.vaccine.name)
        ?.redirectUrl;

    CustomWebViewWithScaffold.launch(
      url: WebUri(
        (urlFromUnleash ?? ApiConstant.vaccineJourneyWebDomain) +
            (queryString != null ? "?$queryString" : ""),
      ),
      cookieWebUrl: urlFromUnleash ?? ApiConstant.vaccineJourneyWebDomain,
    );
  }

  static Future<void> openFitChallenge(int challengeId) async {
    final homeFitApp = Get.find<HomeFitApp>();
    final result = await homeFitApp.getChallengeDetail(challengeId);

    await Get.toNamed<void>(
      AppRoutes.challenge,
      arguments: result,
    );
  }

  static Future<void> openFitWorkout(String type, String name) async {
    final homeApp = Get.find<HomeApp>();
    final commonApp = Get.find<CommonApp>();
    final healthApp = Get.find<HealthApp>();
    final homeFitApp = Get.find<HomeFitApp>();

    commonApp.createCookie();
    if (homeApp.dailyFitTask.isNotEmpty &&
        type != "Rest Day" &&
        homeApp.dailyFitTask.first.routineType != "Rest Day") {
      for (var element in homeApp.dailyFitTask) {
        if (element.routineName == name) {
          String routineId = element.routineId as String;
          await Get.toNamed<void>(
            AppRoutes.fit,
            arguments: FitArguments(
              routineId: routineId,
            ),
          );
        }
      }
    } else if (homeApp.dailyFitTask.isNotEmpty &&
        type == "Rest Day" &&
        homeApp.dailyFitTask.first.routineType == "Rest Day") {
      await homeFitApp.getChallenge();
      homeFitApp.challengesDialog.open();
    } else {
      if (homeApp.isUserHaveFitPlan.value) {
        await Get.to<void>(() => const HomeFitView());
      } else {
        await CustomWebViewWithScaffold.launch(
          url: WebUri(commonApp.fitQuestionDomainName),
          cookieWebUrl: ApiConstant.fitWebDomain,
        );
      }
    }
    healthApp.getDeviceInfo();
  }

  static void openCoupon() {
    Get.to(const ECouponWebviewWithScaffold());
  }

  static void openCoin() {
    Get.to<void>(const CoinsScreen());
  }

  static void openHome() {
    final HomeApp homeApp = Get.find<HomeApp>();
    homeApp.getDataHome();
    Get.offAll<void>(
      const HomeStackBar(),
      transition: Transition.noTransition,
    );
  }

  static checkAccess(
      HomeServiceItemModel serviceData,
      AuthApp authApp,
      CommonApp commonApp,
      bool is_accept_term,
      bool is_access_wearable,
      ConnectDeviceService connectDeviceService) async {
    var enable = connectDeviceService.isEnable();
    if (is_accept_term && is_access_wearable && enable) {
      connectDeviceService.connectDevice();
      bool permitted = await PermissionUtil.tryPermission(Permission.storage) ||
          await PermissionUtil.tryPermission(Permission.manageExternalStorage);
      if (permitted) {
        wearableWeb(serviceData, authApp, commonApp);
      }
    } else {
      openConnectDevice(!is_accept_term);
    }
  }

  static wearableWeb(
      HomeServiceItemModel serviceData, AuthApp authApp, CommonApp commonApp) {
    var url =
        "${ApiConstant.wearableDomain}app/overall?id_token=${authApp.cognitoIdToken.value}&refresh_token=${authApp.refreshToken}&lang=${commonApp.selectedLanguage.value}";
    print(url);
    CustomWebViewWithScaffold.launch(
      url: WebUri.uri(Uri.parse(url)),
      title: serviceData.name,
      service: WellServiceKey.wearable.name,
      cookieWebUrl: ApiConstant.wearableDomain,
    );
  }

  static bool redirectHandler({
    required String redirectUrl,
    required bool showAppBar,
    required bool isInApp,
    bool backOnBaseAndHome = true,
    String? title,
  }) {
    WebUri url = WebUri(redirectUrl);
    if (!url.isValidUri) return false;

    if (isWellDhvLife(url)) {
      final initialRoute = getInitialRouteWithQuery(url);
      if (backOnBaseAndHome) {
        if (initialRoute == "/?") {
          Get.back();
          return true;
        } else if (initialRoute == "/home?") {
          openHome();
          return true;
        }
      }
      return WellNavigationService.handleRoute(initialRoute);
    } else {
      final WellLaunchUrlConfig config = (isInApp)
          ? (showAppBar)
              ? WellLaunchUrlConfig.landing
              : WellLaunchUrlConfig.miniapp
          : WellLaunchUrlConfig.external;
      WellNavigationService.launchUrlWithConfig(
        url,
        config: config,
        title: title,
      );
      return true;
    }
  }

  static void logUTM(String queryString) async {
    try {
      final Uri parsedUri = Uri.parse("?$queryString");
      final query = parsedUri.queryParameters;
      final utmSource = query["utm_source"];
      final utmSourceValid = utmSource != null && utmSource.isNotEmpty;
      final utmMedium = query["utm_medium"];
      final utmMediumValid = utmMedium != null && utmMedium.isNotEmpty;
      final utmCampaign = query["utm_campaign"];
      final utmCampaignValid = utmCampaign != null && utmCampaign.isNotEmpty;
      if (utmSourceValid && utmMediumValid && utmCampaignValid) {
        FirebaseAnalytics.instance.logCampaignDetails(
          source: utmSource,
          medium: utmMedium,
          campaign: utmCampaign,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  static bool isWellDhvLife(Uri url) =>
      (url.scheme == "http" || url.scheme == "https") &&
      url.host == "well.dhv.life";

  static String getInitialRouteWithQuery(Uri url) => "${url.path}?${url.query}";
}

class _WaitForHnScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    UserApp userApp = Get.find<UserApp>();
    return Scaffold(
      backgroundColor: AppColor.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColor.white,
        leading: CustomCircleButton(
          onPressed: () {
            Get.back<void>();
          },
        ),
        centerTitle: true,
        title: Text(
          "Samitivej",
          style: AppText.theme(context).titleLarge?.copyWith(
                color: AppColor.black,
              ),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Obx(() {
            bool? isHNConnected = userApp.isHnConnected;
            if (isHNConnected == null) {
              return Image.asset(
                AppAsset.loadingAnimation,
                fit: BoxFit.fitWidth,
                width: 100.w,
              );
            } else {
              Get.back<bool>(result: isHNConnected);
              return const SizedBox.shrink();
            }
          }),
        ),
      ),
    );
  }
}

class _WaitForAppointmentDetail extends StatelessWidget {
  const _WaitForAppointmentDetail();

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      showAppBar: true,
      title: AppLocalizations.of(context)!.appointmentDetail,
      body: Center(
        child: Image.asset(
          AppAsset.loadingAnimation,
          fit: BoxFit.fitWidth,
          width: 100.w,
        ),
      ),
    );
  }
}
